# 在导入其他模块前隐藏控制台窗口
try:
    import ctypes
    import sys
    
    # 无条件隐藏控制台窗口(不再检查是否为打包环境)
    hwnd = ctypes.windll.kernel32.GetConsoleWindow()
    if hwnd != 0:
        ctypes.windll.user32.ShowWindow(hwnd, 0)  # SW_HIDE = 0
except Exception as e:
    print(f"隐藏控制台窗口失败: {e}")

import os
import sys
import random
import math
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser, font, simpledialog  # 引入 simpledialog 用于输入预设名称
import threading
import subprocess
import time
import tempfile
import platform
import traceback
import shutil
import logging  # 添加logging模块导入
import concurrent.futures  # 添加线程池支持
import datetime  # 添加datetime模块导入
import glob  # 添加glob模块导入
import json  # 添加json模块导入
import re  # 添加re模块导入
import ctypes  # 添加ctypes模块导入
import gc  # 添加gc模块导入
import multiprocessing  # 添加multiprocessing模块导入
import uuid  # 添加uuid模块导入

# DPI相关代码已删除


class VideoProcessingThread(threading.Thread):
    def __init__(self, target_file, subfolders, options, update_progress, completion_callback):
        # 初始化线程
        super(VideoProcessingThread, self).__init__()
        self.target_file = target_file
        self.subfolders = subfolders
        self.options = options
        self.update_progress = update_progress
        self.completion_callback = completion_callback
        self.is_canceled = False
        self.temp_dirs = []
        
        # 为本次运行创建一个唯一的根临时目录
        output_dir = self.options.get('output_dir')
        self.temp_dir = tempfile.mkdtemp(prefix="文运躺音_", dir=output_dir if output_dir else None)
        self.temp_dirs.append(self.temp_dir)
        
        # 创建日志记录器
        self.logger = Logger()
        
        # 初始化startupinfo，用于隐藏控制台窗口
        self.startupinfo = None
        self.creation_flags = 0
        
        # 在Windows系统上隐藏子进程控制台窗口
        if platform.system() == 'Windows':
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            self.startupinfo.wShowWindow = subprocess.SW_HIDE
            # 只使用CREATE_NO_WINDOW标志，避免控制台窗口闪现
            self.creation_flags = subprocess.CREATE_NO_WINDOW
    
    def _get_audio_files(self, subfolder):
        """获取子文件夹中的音频文件列表"""
        audio_folder = os.path.join(subfolder, "音频")
        if os.path.exists(audio_folder):
            return [f for f in os.listdir(audio_folder) if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))]
        return []
    
    def _natural_sort_key(self, s):
        """自然排序的键函数，使数字序列按照数值大小排序"""
        # 将字符串拆分为文本和数字部分
        def convert(text):
            return int(text) if text.isdigit() else text.lower()
        
        import re
        # 将字符串分解为字符和数字序列，然后分别处理
        return [convert(c) for c in re.split(r'(\d+)', s)]
    
    def run(self):
        """运行视频处理线程"""
        self.logger.info(f"开始处理文件夹: {self.target_file}")
        
        start_time = time.time()
        processed_subfolder_count = 0
        processed_audio_count = 0
        self.failed_audio_count_local = 0  # 初始化失败计数
        
        # 用于存储所有合并后的视频路径
        all_merged_original_videos = []
        
        try:
            # 初始化进度
            self.update_progress(0, "初始化处理环境...")
            
            # 计算每个任务的进度分配
            total_subfolders = len(self.subfolders)
            total_audio_files = sum([len(self._get_audio_files(subfolder)) for subfolder in self.subfolders])
            
            self.logger.info(f"总子文件夹数: {total_subfolders}, 总音频文件数: {total_audio_files}")
            
            # 最大进度为90%，保留10%用于最终合并和清理
            max_progress = 90
            
            # 计算每个子文件夹处理完成后的进度增量
            progress_per_subfolder = max_progress / total_subfolders if total_subfolders > 0 else 0
            
            # 进度条指标
            current_progress = 0
            
            # 更新初始进度
            self.update_progress(current_progress, f"开始处理... 剩余文件夹: {total_subfolders}, 剩余音频: {total_audio_files}")
            
            # 并行处理器
            if self.options.get('parallel_processing', False):
                # 获取用户设置的线程数
                requested_workers = self.options.get('max_workers', 4)
                # 使用合理的默认值，最多使用4个线程
                max_workers = min(4, requested_workers)
                self.logger.info(f"启用并行处理，最大线程数: {max_workers} (CPU核心数: {os.cpu_count()})")
            else:
                max_workers = 1
                self.logger.info("使用单线程顺序处理")
            
            # 存储max_workers为实例变量，以便其他方法可以访问
            self.max_workers = max_workers
            
            # 记录处理参数
            param_info = [
                f"处理目标: {self.target_file}",
                f"有效子文件夹: {len(self.subfolders)}",
                f"并行处理: {'是' if max_workers > 1 else '否'}, 最大线程数: {max_workers}",
                f"硬件加速: {'是' if self.options.get('hardware_acceleration', False) else '否'}",
            ]
            
            # 只添加选中的选项到日志
            if self.options.get('add_bgm', False):
                param_info.append(f"添加BGM: 是")
                bgm_path = self.options.get('bgm_path', '')
                if bgm_path:
                    param_info.append(f"BGM路径: {os.path.basename(bgm_path)}")
                param_info.append(f"BGM音量: {int(self.options.get('bgm_volume', 0.2) * 100)}%")
                
            if self.options.get('add_subtitles', False):
                param_info.append(f"添加字幕: 是")
                subtitle_style = self.options.get('subtitle_style', {})
                if subtitle_style:
                    param_info.append(f"字幕样式: {subtitle_style.get('font', '默认字体')}, 大小:{subtitle_style.get('font_size', 16)}")
            
            if self.options.get('create_merged_video', False):
                param_info.append(f"创建合并视频: 是")
            
            for param in param_info:
                self.logger.info(param)
            
            # 进度计算 - 优化进度分配
            # 分配进度百分比：音频处理80%，合并视频20%
            audio_progress_total = 80
            merge_progress_total = 20
            
            # 计算每个音频文件的进度增量
            audio_progress_per_file = audio_progress_total / total_audio_files if total_audio_files > 0 else 0
            
            # 计算每个子文件夹合并的进度增量
            merge_progress_per_folder = merge_progress_total / total_subfolders if total_subfolders > 0 and self.options.get('create_merged_video', False) else 0
            
            # ----- 新的全局任务队列 -----
            # 创建全局任务结构 [(subfolder, audio_file, audio_index, subfolder_index), ...]
            global_tasks = []
            processed_videos_by_subfolder = {}  # 按子文件夹存储处理结果
            
            # 预创建所有子文件夹的输出和临时目录
            subfolder_info = {}  # 存储子文件夹信息
            
            # 构建全局任务队列
            for subfolder_index, subfolder in enumerate(self.subfolders):
                if self.is_canceled:
                    break
                
                # 检查音频和视频文件夹
                audio_folder = os.path.join(subfolder, "音频")
                video_folder = os.path.join(subfolder, "视频")
                
                if not os.path.exists(audio_folder) or not os.path.exists(video_folder):
                    self.logger.warning(f"子文件夹 {subfolder} 不存在音频或视频文件夹，跳过")
                    continue
                
                # 创建输出目录
                output_dir = os.path.join(subfolder, "处理完成")
                os.makedirs(output_dir, exist_ok=True)
                
                # 获取音频和视频文件
                audio_files = [f for f in os.listdir(audio_folder) if f.endswith('.mp3')]
                video_files = [f for f in os.listdir(video_folder) 
                              if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
                
                if not audio_files or not video_files:
                    self.logger.warning(f"子文件夹 {subfolder} 缺少音频或视频文件，跳过")
                    continue
                
                # 排序音频文件，确保数字顺序正确
                audio_files = sorted(audio_files, key=self._natural_sort_key)
                
                # 保存子文件夹信息
                subfolder_info[subfolder] = {
                    'audio_folder': audio_folder,
                    'video_folder': video_folder,
                    'output_dir': output_dir,
                    'video_files': video_files
                }
                
                # 初始化此子文件夹的处理结果列表
                processed_videos_by_subfolder[subfolder] = []
                
                # 将此子文件夹的所有音频添加到全局任务队列
                for audio_index, audio_file in enumerate(audio_files):
                    # 子文件夹、音频文件、音频索引、子文件夹索引
                    global_tasks.append((subfolder, audio_file, audio_index, subfolder_index))
            
            self.logger.info(f"已创建全局任务队列，共 {len(global_tasks)} 个任务")
            
            # 根据并行设置选择处理模式
            if self.options.get('parallel_processing', True) and max_workers > 1:
                # ----- 并行处理全局任务队列 -----
                self.logger.info(f"使用并行处理模式处理全局任务队列")
                futures_dict = {}  # 使用字典跟踪每个任务
                pending_tasks = global_tasks.copy()  # 待处理任务列表
                
                # 创建线程池
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    # 初始化活跃任务集合
                    active_futures = set()
                    
                    # 继续处理直到所有任务完成
                    while pending_tasks or active_futures:
                        # 填充活跃任务，直到达到最大并行数或没有待处理任务
                        while len(active_futures) < self.max_workers and pending_tasks:
                            # 获取下一个任务
                            task = pending_tasks.pop(0)
                            subfolder, audio_file, audio_index, subfolder_index = task
                            
                            if self.is_canceled:
                                break
                            
                            # 获取子文件夹信息
                            info = subfolder_info[subfolder]
                            
                            # 提交任务
                            self.logger.info(f"提交任务: 子文件夹 {os.path.basename(subfolder)} 的音频 {audio_file} (总剩余任务: {len(pending_tasks)})")
                            future = executor.submit(
                                self.process_audio_file_with_isolation,
                                subfolder, info['audio_folder'], info['video_folder'], info['output_dir'],
                                audio_file, audio_index, processed_audio_count, total_audio_files,
                                info['video_files']
                            )
                            
                            # 跟踪此任务
                            futures_dict[future] = (subfolder, audio_file)
                            active_futures.add(future)
                        
                        # 等待任意一个任务完成
                        if active_futures:
                            # 使用wait获取已完成的任务
                            done, not_done = concurrent.futures.wait(
                                active_futures, 
                                timeout=0.5,  # 短暂超时以便定期检查新任务
                                return_when=concurrent.futures.FIRST_COMPLETED
                            )
                            
                            # 处理已完成的任务
                            for future in done:
                                subfolder, audio_file = futures_dict[future]
                                try:
                                    # 获取处理结果（输出视频路径）
                                    result = future.result()
                                    if result:
                                        # 将结果添加到对应子文件夹的结果列表
                                        processed_videos_by_subfolder[subfolder].append(result)
                                        processed_audio_count += 1
                                        
                                        # 更新进度和剩余数量
                                        current_progress += audio_progress_per_file
                                        remaining_folders = total_subfolders - processed_subfolder_count
                                        remaining_audio_files = total_audio_files - processed_audio_count
                                        self.update_progress(current_progress, f"处理音频 [{processed_audio_count}/{total_audio_files}], 剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio_files}")
                                        
                                        # 释放资源以便立即处理新任务
                                        self.logger.info(f"任务完成: {os.path.basename(subfolder)}/{audio_file} - 即将启动新任务...")
                                except Exception as e:
                                    self.logger.error(f"处理音频文件 {os.path.basename(subfolder)}/{audio_file} 时出错: {str(e)}")
                                    traceback.print_exc()
                                
                                # 从活跃任务中移除
                                active_futures.remove(future)
                                del futures_dict[future]
                        
                        # 如果被取消，退出循环
                        if self.is_canceled:
                            self.logger.info("处理被取消，终止所有任务")
                            break
                    
                    # 取消所有未完成的任务
                    for future in active_futures:
                        future.cancel()
            else:
                # ----- 单线程处理全局任务队列 -----
                self.logger.info(f"使用单线程模式处理全局任务队列")
                for task in global_tasks:
                    if self.is_canceled:
                        break
                        
                    subfolder, audio_file, audio_index, subfolder_index = task
                    info = subfolder_info[subfolder]
                    
                    self.logger.info(f"处理: 子文件夹 {os.path.basename(subfolder)} 的音频 {audio_file}")
                    result = self.process_audio_file(
                        subfolder, info['audio_folder'], info['video_folder'], info['output_dir'],
                        audio_file, audio_index, processed_audio_count, total_audio_files,
                        info['video_files']
                    )
                    
                    if result:
                        processed_videos_by_subfolder[subfolder].append(result)
                        processed_audio_count += 1
                        
                        # 更新进度和剩余数量
                        current_progress += audio_progress_per_file
                        remaining_folders = total_subfolders - processed_subfolder_count
                        remaining_audio_files = total_audio_files - processed_audio_count
                        self.update_progress(current_progress, f"处理音频 [{processed_audio_count}/{total_audio_files}], 剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio_files}")
            
            # 总是处理文件夹，即使有部分音频处理失败
            self.logger.info(f"音频处理完成，继续处理子文件夹合并和原视频合并任务")
            processed_subfolder_count = 0

            # 处理每个子文件夹的合并视频选项
            for subfolder_index, subfolder in enumerate(self.subfolders):
                if self.is_canceled:
                    break
                
                # 获取此子文件夹的处理结果
                processed_videos = processed_videos_by_subfolder.get(subfolder, [])
                
                output_dir = os.path.join(subfolder, "处理完成")
                os.makedirs(output_dir, exist_ok=True)
                
                # 检查是否有处理后的视频，并记录情况
                if not processed_videos:
                    self.logger.warning(f"子文件夹 {os.path.basename(subfolder)} 没有成功处理的视频")
                else:
                    self.logger.info(f"子文件夹 {os.path.basename(subfolder)} 有 {len(processed_videos)} 个成功处理的视频")
                
                # 合并该子文件夹中所有处理后的视频（如果选项开启且有处理后的视频）
                if processed_videos and self.options.get('create_merged_video', False):
                    merged_video_path = os.path.join(output_dir, "合并视频.mp4")
                    self.logger.info(f"合并子文件夹 {os.path.basename(subfolder)} 的视频到: {merged_video_path}")
                    
                    # 更新进度 - 在merge_videos方法内部会更新更精细的进度
                    self.update_progress(current_progress, f"合并子文件夹视频 [{processed_subfolder_count + 1}/{total_subfolders}]")
                    
                    self.merge_videos(processed_videos, merged_video_path)
                    
                    # 更新合并后的进度
                    current_progress += merge_progress_per_folder
                    self.update_progress(current_progress, f"完成子文件夹 [{processed_subfolder_count + 1}/{total_subfolders}]")
                
                # 寻找并合并原视频与1号音频处理视频（仅在设置中开启时执行）
                # 即使没有处理后的视频，也尝试合并原视频与1.mp4（如果存在）
                if self.options.get('merge_original_video', False):
                    self.logger.info(f"尝试合并子文件夹 {os.path.basename(subfolder)} 的原视频与1号处理视频")
                    merged_original_path = self.merge_original_with_first_video(subfolder, output_dir)
                    if merged_original_path:
                        self.logger.info(f"成功合并原视频与1号处理视频: {merged_original_path}")
                        all_merged_original_videos.append(merged_original_path)
                    else:
                        self.logger.warning(f"子文件夹 {os.path.basename(subfolder)} 原视频合并失败或跳过")
                
                processed_subfolder_count += 1
            
            # 如果有多个合并后的视频，且用户选择了创建合并视频和合并原视频，则将它们也合并在一起
            if len(all_merged_original_videos) > 1 and self.options.get('create_merged_video', False) and self.options.get('merge_original_video', False):
                self.logger.info("开始合并所有原视频与1号视频的结果...")
                self.update_progress(96, "合并所有原视频与1号视频的结果")
                
                # 创建最终输出目录（如果不存在）
                final_output_dir = os.path.join(self.target_file, "最终输出")
                os.makedirs(final_output_dir, exist_ok=True)
                
                # 最终合并输出文件路径
                final_merged_path = os.path.join(final_output_dir, "所有原视频与1号视频合并.mp4")
                
                # 合并所有视频
                self.merge_videos(all_merged_original_videos, final_merged_path)
                self.logger.info(f"所有原视频与1号视频合并成功: {final_merged_path}")
            
            # 计算总处理时间
            total_time = time.time() - start_time
            minutes, seconds = divmod(total_time, 60)
            hours, minutes = divmod(minutes, 60)
            
            time_str = f"{int(hours)}小时{int(minutes)}分{int(seconds)}秒" if hours > 0 else f"{int(minutes)}分{int(seconds)}秒"
            success_message = f"处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件, 总耗时: {time_str}"
            
            self.logger.info(success_message)
            
            # 最终更新进度为100%
            self.update_progress(100, f"处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件")
            
            # 处理完成后清理临时文件（如果需要）
            if self.options.get('clean_temp_files', True):
                try:
                    if self.temp_dirs and all(os.path.exists(temp_dir) for temp_dir in self.temp_dirs):
                        for temp_dir in self.temp_dirs:
                            shutil.rmtree(temp_dir)
                            self.logger.info(f"清理临时目录: {temp_dir}")
                except Exception as e:
                    self.logger.warning(f"清理临时目录失败: {self.temp_dirs}, 错误: {str(e)}")
            else:
                self.logger.info(f"保留临时目录: {self.temp_dirs}")
            
            # 完成回调
            if self.completion_callback:
                self.completion_callback(True, success_message)
                
            # 最后处理完成后，记录总结信息
            end_time = time.time()
            total_time = end_time - start_time
            hours, remainder = divmod(total_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            # 获取失败计数
            failed_audio_count = getattr(self, 'failed_audio_count_local', 0)
            
            # 总结处理结果
            result_summary = f"处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件"
            if failed_audio_count > 0:
                result_summary += f", {failed_audio_count}个文件失败"
            result_summary += f", 总耗时: {int(hours)}时{int(minutes)}分{int(seconds)}秒"
            
            self.logger.info(result_summary)
            
            # 检查是否因磁盘空间不足导致失败
            disk_space_error = False
            for temp_dir in self.temp_dirs:
                if os.path.exists(temp_dir):
                    free_space = self._get_free_disk_space(temp_dir)
                    if free_space < 1 * 1024 * 1024 * 1024:  # 小于1GB
                        disk_space_error = True
                        break
            
            if disk_space_error:
                self.logger.error("处理失败: 磁盘空间严重不足，请清理磁盘空间后重试")
                if self.completion_callback:
                    self.completion_callback(False, "处理失败: 磁盘空间不足，请清理磁盘空间后重试")
                return
            
            # 即使有部分失败，也触发完成回调
            success = failed_audio_count == 0
            message = result_summary
            if not success:
                message = f"部分文件处理失败。{result_summary}，请检查日志获取详细信息。"
            
            # 清理临时目录
            self._clean_temp_dirs()
            
            # 调用完成回调
            if self.completion_callback:
                self.completion_callback(success, message)
                
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {str(e)}")
            traceback.print_exc()
            
            # 发生异常时，确保仍然调用完成回调
            if self.completion_callback:
                end_time = time.time()
                total_time = end_time - start_time
                hours, remainder = divmod(total_time, 3600)
                minutes, seconds = divmod(remainder, 60)
                error_message = f"处理失败: {str(e)}，总耗时: {int(hours)}时{int(minutes)}分{int(seconds)}秒"
                self.completion_callback(False, error_message)
            
            # 清理临时目录
            self._clean_temp_dirs()
        finally:
            # 确保在任何情况下都会清理临时文件
            try:
                # 先等待1秒钟确保所有文件操作完成
                time.sleep(1)
                self._clean_temp_dirs()
            except Exception as e:
                self.logger.warning(f"最终清理临时目录时出错: {str(e)}")
    
    def process_audio_file_with_isolation(self, subfolder, audio_folder, video_folder, output_dir, audio_file, audio_index, processed_audio_count, total_audio_files, video_files):
        """
        在一个隔离的环境中处理单个音频文件，包括创建和清理独立的临时目录。
        """
        # 使用UUID确保每个处理任务都有一个完全唯一的临时目录，避免并发冲突
        audio_name_for_dir = os.path.splitext(os.path.basename(audio_file))[0]
        # 结合音频文件名和UUID，创建既唯一又可读的目录名
        task_id = f"{audio_name_for_dir}_{uuid.uuid4().hex[:8]}"
        task_temp_dir = os.path.join(self.temp_dir, f"task_{task_id}")
        os.makedirs(task_temp_dir, exist_ok=True)
        
        try:
            return self.process_audio_file(
                subfolder, audio_folder, video_folder, output_dir,
                audio_file, audio_index, processed_audio_count, total_audio_files,
                video_files,
                thread_temp_dir=task_temp_dir # 传递隔离的临时目录
            )
        finally:
            # 清理这个任务的临时目录
            try:
                if os.path.exists(task_temp_dir):
                    shutil.rmtree(task_temp_dir)
                    self.logger.debug(f"清理了任务临时目录: {task_temp_dir}")
            except Exception as e:
                self.logger.error(f"清理任务临时目录 {task_temp_dir} 失败: {e}")
    
    def process_audio_file(self, subfolder, audio_folder, video_folder, output_dir, audio_file, audio_index, processed_audio_count, total_audio_files, video_files, thread_temp_dir=None):
        """核心处理函数：为单个音频文件匹配视频、添加BGM和字幕"""
        
        # 定义一个唯一的临时目录，用于存储此任务的中间文件
        subfolder_temp_dir = tempfile.mkdtemp(prefix="临时_", dir=output_dir)
        self.temp_dirs.append(subfolder_temp_dir)
        
        audio_path = os.path.join(audio_folder, audio_file)
        audio_base_name = os.path.splitext(audio_file)[0]
        self.logger.info(f"开始处理音频文件: {audio_file} (输出基础名: {audio_base_name})")

        try:
            # 记录处理开始时间
            process_start_time = time.time()
            
            # 使用循环分配视频，确保所有视频都被使用
            video_path = None
            
            if len(video_files) > 0:
                # 使用音频索引取模，确保视频均匀分配
                video_index = audio_index % len(video_files)
                random_video = video_files[video_index]
                video_path = os.path.join(video_folder, random_video)
                self.logger.info(f"为音频{audio_index+1}分配视频{video_index+1}: {random_video}")
            else:
                self.logger.error(f"没有可用的视频文件")
                return None
            
            # 查找字幕文件 - 先检查与音频同名的字幕，再检查字幕文件夹
            subtitle_path = None
            # 首先检查音频文件夹中是否有同名字幕文件
            potential_subtitle_path = os.path.join(audio_folder, f"{audio_base_name}.srt")
            if os.path.exists(potential_subtitle_path):
                subtitle_path = potential_subtitle_path
                self.logger.info(f"找到音频同名字幕文件: {subtitle_path}")
            else:
                # 然后检查字幕文件夹
                subtitle_folder = os.path.join(subfolder, "字幕")
                if os.path.exists(subtitle_folder):
                    potential_subtitle_path = os.path.join(subtitle_folder, f"{audio_base_name}.srt")
                    if os.path.exists(potential_subtitle_path):
                        subtitle_path = potential_subtitle_path
                        self.logger.info(f"找到字幕文件夹中的字幕文件: {subtitle_path}")
            
            if subtitle_path is None and self.options.get('add_subtitles', False):
                self.logger.warning(f"未找到对应的字幕文件: {audio_base_name}.srt")
            
            # 使用传入的临时目录或创建新的
            subfolder_temp_dir = thread_temp_dir if thread_temp_dir else os.path.join(output_dir, f"临时_{audio_base_name}_{int(time.time())}")
            os.makedirs(subfolder_temp_dir, exist_ok=True)
            self.temp_dirs.append(subfolder_temp_dir)
                
            # 最终输出视频路径
            output_video_path = os.path.join(output_dir, f"{audio_base_name}.mp4")
            
            # 处理临时输出路径 
            temp_output = os.path.join(subfolder_temp_dir, f"temp_{audio_base_name}.mp4")
            
            # 直接调用一步处理函数，将视频循环同时添加音频
            self.logger.info(f"开始一步合成: 视频 {os.path.basename(video_path)} + 音频 {audio_file}")
            
            # 获取音频时长，用于记录日志
            try:
                audio_info = self._get_video_info(audio_path)
                if audio_info and 'duration' in audio_info:
                    audio_duration = float(audio_info.get('duration', 0))
                    self.logger.info(f"音频时长: {audio_duration}秒")
                    
                    # 如果音频时长超过10分钟，给出警告
                    if audio_duration > 600:
                        self.logger.warning(f"警告：音频时长较长 ({audio_duration:.2f}秒)，处理可能需要较长时间")
            except Exception as e:
                self.logger.warning(f"获取音频时长时出错: {str(e)}")
                
            processed_output = os.path.join(subfolder_temp_dir, f"temp_{audio_index + 1}.mp4")
            success = self.process_video_with_audio(video_path, audio_path, processed_output)
            
            if not success:
                self.logger.error(f"一步合成失败: {processed_output}")
                return None
                
            self.logger.info(f"一步合成成功: {processed_output}")
            
            # 处理后续步骤（添加BGM和字幕）
            current_file = processed_output
            
            # 添加背景音乐 (BGM) - 在添加字幕之前
            if self.options.get('add_bgm', False) and 'bgm_path' in self.options and os.path.exists(self.options['bgm_path']):
                # BGM输出路径
                bgm_output = os.path.join(subfolder_temp_dir, f"with_bgm_{audio_base_name}.mp4")
                
                # 添加背景音乐
                self.logger.info(f"添加背景音乐到视频: {os.path.basename(current_file)}")
                self.update_progress(60, "添加背景音乐...")
                
                # 获取BGM音量
                bgm_volume = self.options.get('bgm_volume', 0.2)
                self.logger.info(f"使用BGM音量: {bgm_volume}")
                
                # 执行BGM添加
                bgm_success = self._add_bgm_to_video(
                    current_file,  # 输入视频
                    bgm_output,               # 输出路径
                    self.options['bgm_path'], # BGM路径
                    bgm_volume                # 音量
                )
                
                if bgm_success:
                    self.logger.info(f"背景音乐添加成功: {os.path.basename(bgm_output)}")
                    current_file = bgm_output  # 更新当前处理视频为添加了BGM的视频
                else:
                    self.logger.error(f"背景音乐添加失败，将使用无BGM版本继续")
            else:
                if not self.options.get('add_bgm', False):
                    self.logger.info("未勾选添加BGM选项，跳过BGM添加")
                elif 'bgm_path' not in self.options:
                    self.logger.warning("未设置BGM路径，跳过BGM添加")
                elif not os.path.exists(self.options.get('bgm_path', '')):
                    self.logger.warning(f"BGM文件不存在: {self.options.get('bgm_path', '')}, 跳过BGM添加")
            
            # 如果需要添加字幕
            if self.options.get('add_subtitles', False) and subtitle_path:
                subtitle_output = os.path.join(subfolder_temp_dir, f"with_subtitle_{audio_base_name}.mp4")
                
                self.logger.info(f"开始添加字幕: {os.path.basename(subtitle_path)} 到 {os.path.basename(current_file)}")
                self.update_progress(80, "添加字幕...")
                
                # 使用字幕添加方法
                subtitle_success = self._add_subtitles_with_moviepy(current_file, subtitle_output, subtitle_path)
                
                if subtitle_success:
                    self.logger.info(f"字幕添加成功: {os.path.basename(subtitle_output)}")
                    # 复制最终文件到输出目录
                    shutil.copy2(subtitle_output, output_video_path)
                else:
                    self.logger.error(f"字幕添加失败，使用无字幕版本")
                    # 复制无字幕版本
                    shutil.copy2(current_file, output_video_path)
            else:
                # 直接复制到输出目录
                shutil.copy2(current_file, output_video_path)
            
            self.logger.info(f"最终输出文件: {output_video_path}")
            
            # 完成处理，将处理时间记录到日志
            process_end_time = time.time()
            process_duration = process_end_time - process_start_time
            minutes, seconds = divmod(process_duration, 60)
            self.logger.info(f"音频文件 {audio_file} 处理完成，耗时: {int(minutes)}分{int(seconds)}秒")
            
            # 返回最终处理后的视频路径
            return output_video_path
            
        except Exception as e:
            self.logger.error(f"处理音频文件 {audio_file} 时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    def cancel(self):
        """强制取消处理"""
        self.is_canceled = True
        self.logger.info("取消处理请求已接收，处理将在下一个安全点终止")
        
        # 尝试终止当前正在运行的FFmpeg进程
        try:
            # 获取隐藏窗口的启动信息
            startupinfo, creation_flags = get_hidden_startupinfo()
            
            # 查找FFmpeg进程并终止
            if platform.system() == "Windows":
                # 先使用/T参数终止整个进程树
                subprocess.run(["taskkill", "/F", "/T", "/IM", "ffmpeg.exe"], 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              startupinfo=startupinfo, creationflags=creation_flags)
                
                # 等待一小段时间
                time.sleep(1)
                
                # 再次尝试终止所有ffmpeg进程
                subprocess.run(["taskkill", "/F", "/IM", "ffmpeg.exe"], 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              startupinfo=startupinfo, creationflags=creation_flags)
                
                # 使用wmic强制终止任何残留进程
                try:
                    subprocess.run('wmic process where name="ffmpeg.exe" delete', 
                                  shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  startupinfo=startupinfo, creationflags=creation_flags)
                except:
                    pass
                    
                self.logger.info("已强制终止所有FFmpeg进程")
            else:
                # Unix系统使用killall
                try:
                    subprocess.run(["killall", "ffmpeg"], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    time.sleep(0.5)
                    subprocess.run(["killall", "-9", "ffmpeg"], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    self.logger.info("已强制终止所有FFmpeg进程")
                except:
                    pass
        except Exception as e:
            self.logger.warning(f"尝试终止FFmpeg进程时出错: {str(e)}")
            
        # 等待确保进程完全退出
        time.sleep(2)
        
        # 清理临时目录
        self._clean_temp_dirs()
    
    def _clean_temp_dirs(self):
        """清理临时目录"""
        if not hasattr(self, 'temp_dirs') or not self.temp_dirs:
            return
            
        self.logger.info(f"开始清理临时目录，总数: {len(self.temp_dirs)}")
        
        # 获取处理完成文件夹中的所有临时目录
        all_temp_dirs = []
        
        # 1. 先添加已记录的临时目录
        for temp_dir in self.temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                all_temp_dirs.append(temp_dir)
                
        # 2. 搜索所有子文件夹中的临时目录（以"临时_"开头的文件夹）
        try:
            for subfolder in self.subfolders:
                if os.path.exists(subfolder):
                    output_dir = os.path.join(subfolder, "处理完成")
                    if os.path.exists(output_dir):
                        for item in os.listdir(output_dir):
                            item_path = os.path.join(output_dir, item)
                            if os.path.isdir(item_path) and item.startswith("临时_"):
                                if item_path not in all_temp_dirs:
                                    all_temp_dirs.append(item_path)
                                    self.logger.info(f"找到额外的临时目录: {item_path}")
        except Exception as e:
            self.logger.warning(f"搜索额外临时目录时出错: {str(e)}")
        
        # 记录总共找到的临时目录数量
        self.logger.info(f"共找到 {len(all_temp_dirs)} 个临时目录需要清理")
        
        # 清理所有临时目录
        for temp_dir in all_temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    self.logger.info(f"清理临时目录: {temp_dir}")
                    
                    # 先遍历删除所有文件
                    try:
                        for root, dirs, files in os.walk(temp_dir, topdown=False):
                            for file in files:
                                try:
                                    file_path = os.path.join(root, file)
                                    if os.path.exists(file_path):
                                        os.remove(file_path)
                                except Exception as e:
                                    self.logger.warning(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
                        
                        # 使用shutil.rmtree递归删除目录及其内容
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        self.logger.info(f"临时目录已清理: {temp_dir}")
                    except Exception as e:
                        # 如果rmtree失败，尝试使用系统命令强制删除
                        self.logger.warning(f"使用常规方法删除临时目录失败，尝试使用系统命令: {str(e)}")
                        try:
                            if platform.system() == "Windows":
                                # Windows上使用rd /s /q命令强制删除
                                subprocess.run(f'rd /s /q "{temp_dir}"', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                            else:
                                # Linux/macOS上使用rm -rf命令强制删除
                                subprocess.run(f'rm -rf "{temp_dir}"', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                            
                            if not os.path.exists(temp_dir):
                                self.logger.info(f"使用系统命令成功清理临时目录: {temp_dir}")
                            else:
                                self.logger.warning(f"使用系统命令后临时目录仍存在: {temp_dir}")
                        except Exception as cmd_error:
                            self.logger.warning(f"使用系统命令删除临时目录失败: {temp_dir}, 错误: {str(cmd_error)}")
                else:
                    self.logger.info(f"临时目录不存在，无需清理: {temp_dir}")
            except Exception as e:
                self.logger.warning(f"清理临时目录过程中出错: {temp_dir}, 错误: {str(e)}")
        
        # 重置临时目录列表
        self.temp_dirs = []
        self.logger.info("临时目录清理完成")
    

    def check_nvidia_gpu(self):
        try:
            # 检查NVIDIA GPU是否可用
            if platform.system() == 'Windows':
                result = subprocess.run(["nvidia-smi"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:
                result = subprocess.run(["nvidia-smi"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode == 0
        except:
            return False
    
    def check_intel_gpu(self):
        try:
            if platform.system().lower() == "windows":
                # Windows检测Intel GPU
                result = subprocess.run(["wmic", "path", "win32_VideoController", "get", "name"], 
                                       stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                return "Intel" in result.stdout
            else:
                # Linux检测Intel GPU
                result = subprocess.run(["lspci"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                return "Intel" in result.stdout and "VGA" in result.stdout
        except:
            return False
    
    def check_amd_gpu(self):
        try:
            if platform.system().lower() == "windows":
                # Windows检测AMD GPU - 使用多种方法确保检测准确性
                # 方法1：使用wmic检查显卡名称
                try:
                    result = subprocess.run(["wmic", "path", "win32_VideoController", "get", "name"], 
                                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, 
                                           startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    if "AMD" in result.stdout or "Radeon" in result.stdout or "RX" in result.stdout:
                        return True
                except:
                    pass
                
                # 方法2：检查FFmpeg编码器支持
                try:
                    result = subprocess.run(["ffmpeg", "-encoders"], 
                                           stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True,
                                           startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    if "h264_amf" in result.stdout:
                        return True
                except:
                    pass
                
                # 方法3：检查注册表中的AMD驱动
                try:
                    import winreg
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\AMD\CN")
                    winreg.CloseKey(key)
                    return True
                except:
                    pass
                
                return False
            else:
                # Linux检测AMD GPU
                result = subprocess.run(["lspci"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                return ("AMD" in result.stdout or "Radeon" in result.stdout) and "VGA" in result.stdout
        except:
            return False
    
    def check_vaapi(self):
        try:
            # 检查VAAPI设备是否存在
            return os.path.exists("/dev/dri/renderD128")
        except:
            return False
            
    def process_audio_with_video(self, audio_file, video_files, output_folder, subfolder_temp_dir, subfolder_name=None):
        """处理单个音频文件与对应的视频文件"""
        try:
            # 获取音频文件名（不含扩展名）
            audio_base_name = os.path.splitext(os.path.basename(audio_file))[0]
            
            # 记录详细日志
            self.logger.info(f"处理音频文件: {audio_file}")
            self.logger.info(f"对应视频文件数: {len(video_files)}")
            
            # 检查音频文件是否存在
            if not os.path.exists(audio_file):
                self.logger.error(f"音频文件不存在: {audio_file}")
                return None
            
            # 查找对应的字幕文件 (.srt)
            subtitle_file = None
            
            # 首先在音频文件所在的同一目录中查找同名字幕文件
            audio_folder = os.path.dirname(audio_file)
            potential_same_folder_subtitle = os.path.join(audio_folder, f"{audio_base_name}.srt")
            
            if os.path.exists(potential_same_folder_subtitle):
                subtitle_file = potential_same_folder_subtitle
                self.logger.info(f"在音频文件夹中找到匹配的字幕文件: {subtitle_file}")
            else:
                # 如果音频文件夹中没有找到，再在专门的字幕文件夹中查找
                subfolder_path = os.path.dirname(os.path.dirname(audio_file))  # 获取子文件夹路径
                potential_subtitle_folder = os.path.join(subfolder_path, "字幕")
                
                if os.path.exists(potential_subtitle_folder):
                    # 1) 精确匹配（不区分大小写）
                    candidates = glob.glob(os.path.join(potential_subtitle_folder, "*.srt"))
                    exact_match = None
                    lower_audio_name = audio_base_name.lower()
                    for sf in candidates:
                        if os.path.splitext(os.path.basename(sf))[0].lower() == lower_audio_name:
                            exact_match = sf
                            break
                    if exact_match:
                        subtitle_file = exact_match
                        self.logger.info(f"字幕文件夹中找到精确匹配: {os.path.basename(subtitle_file)}")
                    else:
                        # 2) 前缀或包含关系模糊匹配（不区分大小写）
                        fuzzy_match = None
                        for sf in candidates:
                            sub_base_lower = os.path.splitext(os.path.basename(sf))[0].lower()
                            if sub_base_lower.startswith(lower_audio_name) or lower_audio_name in sub_base_lower:
                                fuzzy_match = sf
                                break
                        if fuzzy_match:
                            subtitle_file = fuzzy_match
                            self.logger.info(f"字幕文件夹中找到模糊匹配: {os.path.basename(subtitle_file)}")
                        else:
                            self.logger.warning("字幕文件夹中未找到匹配字幕")
            
            # 扩展搜索：如果还是没找到字幕，尝试在音频文件夹中查找任意.srt文件
            if subtitle_file is None and self.options.get('add_subtitles', False):
                srt_files_in_audio_folder = glob.glob(os.path.join(audio_folder, "*.srt"))
                lower_audio_name = audio_base_name.lower()
                for sf in srt_files_in_audio_folder:
                    if os.path.splitext(os.path.basename(sf))[0].lower() == lower_audio_name:
                        subtitle_file = sf
                        break
                if subtitle_file is None:
                    for sf in srt_files_in_audio_folder:
                        sub_base_lower = os.path.splitext(os.path.basename(sf))[0].lower()
                        if sub_base_lower.startswith(lower_audio_name) or lower_audio_name in sub_base_lower:
                            subtitle_file = sf
                            break
            if subtitle_file is None and self.options.get('add_subtitles', False):
                self.logger.warning("未找到匹配的字幕文件，将跳过字幕添加")
            
            # 处理视频文件
            # 仅使用一个视频 - 根据音频文件名中的数字选择对应的视频
            merged_videos = []
            
            # 尝试从音频文件名中提取数字
            audio_number_match = re.search(r'(\d+)', audio_base_name)
            selected_video_index = 0
            
            if audio_number_match:
                # 从文件名提取数字作为索引
                audio_number = int(audio_number_match.group(1))
                # 取模确保在视频文件数量范围内
                selected_video_index = (audio_number - 1) % len(video_files)
                self.logger.info(f"根据音频序号 {audio_number} 选择视频索引 {selected_video_index}")
            else:
                # 如果音频文件名中没有数字，则根据音频文件在列表中的可能位置选择视频
                try:
                    # 获取音频文件列表
                    audio_folder_path = os.path.dirname(audio_file)
                    audio_files_list = sorted([f for f in os.listdir(audio_folder_path) if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))], key=self._natural_sort_key)
                    
                    # 查找当前音频在排序后列表中的位置
                    audio_filename = os.path.basename(audio_file)
                    if audio_filename in audio_files_list:
                        current_index = audio_files_list.index(audio_filename)
                        selected_video_index = current_index % len(video_files)
                        self.logger.info(f"根据音频在列表中的索引 {current_index} 选择视频索引 {selected_video_index}")
                except Exception as e:
                    self.logger.warning(f"确定视频索引时出错: {str(e)}，使用默认索引0")
            
            # 确保索引在有效范围内
            if selected_video_index >= len(video_files):
                selected_video_index = selected_video_index % len(video_files)
            
            # 获取选定的视频文件
            selected_video = video_files[selected_video_index]
            self.logger.info(f"选择视频文件: {selected_video} (索引 {selected_video_index})")

            # 为确保每个任务独立，复制一份视频文件到临时目录
            unique_video_copy_name = f"{audio_base_name}_temp_video.mp4"
            temp_video_path = os.path.join(subfolder_temp_dir, unique_video_copy_name)
            try:
                shutil.copy2(selected_video, temp_video_path)
                self.logger.info(f"为任务 {audio_base_name} 创建独立的视频副本: {temp_video_path}")
            except Exception as e:
                self.logger.error(f"无法复制视频文件: {e}，处理可能失败")
                return None  # 如果无法复制，则中止此任务
            
            # 构建输出文件路径 (临时)
            temp_output = os.path.join(subfolder_temp_dir, f"merged_{audio_base_name}.mp4")
            
            # 记录处理信息
            self.logger.info(f"一步处理: 视频 {os.path.basename(temp_video_path)} + 音频 {os.path.basename(audio_file)}")
            self.update_progress(40, f"处理音频 {audio_base_name}...")
            
            # 执行一步处理（循环视频同时合并音频）
            success = self.process_video_with_audio(temp_video_path, audio_file, temp_output)
            
            if success:
                merged_videos.append(temp_output)
                self.logger.info(f"一步处理成功: {temp_output}")
            else:
                self.logger.error(f"一步处理失败: {selected_video} + {audio_file}")
                
                # 处理失败时尝试使用下一个视频
                fallback_video_index = (selected_video_index + 1) % len(video_files)
                fallback_video = video_files[fallback_video_index]
                
                self.logger.info(f"尝试使用备用视频: {fallback_video}")
                fallback_output = os.path.join(subfolder_temp_dir, f"fallback_merged_{audio_base_name}.mp4")
                
                fallback_success = self.process_video_with_audio(fallback_video, audio_file, fallback_output)
                if fallback_success:
                    merged_videos.append(fallback_output)
                    self.logger.info(f"备用视频处理成功: {fallback_output}")
            
            if not merged_videos:
                self.logger.error(f"没有成功处理的视频，跳过后续处理")
                return None
            
            # 使用第一个合并成功的视频作为基础
            processed_output = merged_videos[0]
            
            # 处理流程: 先合成视频和音频 -> 然后添加BGM -> 最后添加字幕
            
            # 1. 添加背景音乐 (只有当用户勾选了添加BGM选项时才会添加)
            if self.options.get('add_bgm', False) and 'bgm_path' in self.options and os.path.exists(self.options['bgm_path']):
                # 构建BGM输出路径
                bgm_output = os.path.join(subfolder_temp_dir, f"with_bgm_{audio_base_name}.mp4")
                
                # 添加背景音乐
                self.logger.info(f"添加背景音乐到视频: {os.path.basename(processed_output)}")
                self.update_progress(70, "添加背景音乐...")
                
                # 获取BGM音量设置
                bgm_volume = self.options.get('bgm_volume', 0.2)
                self.logger.info(f"使用BGM音量: {bgm_volume}")
                
                # 执行BGM添加
                bgm_success = self._add_bgm_to_video(
                    processed_output,  # 输入视频
                    bgm_output,        # 输出路径
                    self.options['bgm_path'],  # BGM路径
                    bgm_volume         # 音量
                )
                
                if bgm_success:
                    self.logger.info(f"背景音乐添加成功: {os.path.basename(bgm_output)}")
                    processed_output = bgm_output  # 更新为添加了BGM的视频
                else:
                    self.logger.error(f"背景音乐添加失败，使用无BGM版本继续")
            else:
                if not self.options.get('add_bgm', False):
                    self.logger.info("用户未勾选添加BGM选项，跳过BGM添加")
                elif 'bgm_path' not in self.options:
                    self.logger.warning("未设置BGM路径，跳过BGM添加")
                elif not os.path.exists(self.options.get('bgm_path', '')):
                    self.logger.warning(f"BGM文件不存在: {self.options.get('bgm_path', '')}, 跳过BGM添加")
            
            # 2. 添加字幕
            if self.options.get('add_subtitles', False) and subtitle_file:
                subtitle_input_video = processed_output  # 使用当前处理后的视频
                subtitle_output = os.path.join(subfolder_temp_dir, f"with_subtitle_{audio_base_name}.mp4")
                
                self.logger.info(f"开始添加字幕: {os.path.basename(subtitle_file)} 到 {os.path.basename(subtitle_input_video)}")
                self.update_progress(80, "添加字幕...")
                
                # 使用MoviePy方法添加字幕
                subtitle_success = self._add_subtitles_with_moviepy(subtitle_input_video, subtitle_output, subtitle_file)
                
                if subtitle_success:
                    self.logger.info(f"字幕添加成功: {os.path.basename(subtitle_output)}")
                    processed_output = subtitle_output  # 更新处理后的输出
                else:
                    self.logger.error(f"字幕添加失败，使用无字幕版本继续")
            
            # 3. 复制最终处理结果到输出目录
            final_output = os.path.join(output_folder, f"{audio_base_name}.mp4")
            try:
                shutil.copy2(processed_output, final_output)
                self.logger.info(f"最终输出文件: {final_output}")
                return final_output
            except Exception as e:
                self.logger.error(f"复制最终输出文件时出错: {str(e)}")
                return None
                
        except Exception as e:
            self.logger.error(f"处理音频和视频时出错: {str(e)}")
            traceback.print_exc()
            return None
    

    def _add_subtitles_with_moviepy(self, input_video, output_path, subtitle_path):
        """使用简单的ffmpeg命令添加字幕，不依赖ImageMagick和其他复杂库"""
        self.logger.info(f"使用简单ffmpeg方法添加字幕: {subtitle_path} 到 {input_video}")
        
        temp_subtitle_path = None # 用于确保最后能清理

        try:
            # 获取字幕样式
            subtitle_style = self.options.get('subtitle_style', {})
            self.logger.info(f"应用字幕样式: {subtitle_style}")
            
            # 获取自定义码率
            bitrate = "2000k"
            if self.options.get('custom_bitrate', False):
                bitrate_value = self.options.get('bitrate', '2000')
                bitrate = f"{bitrate_value}k"
                self.logger.info(f"在编码器设置中使用自定义码率: {bitrate}")
            
            # 检查是否可以使用硬件加速
            hw_encoder, hw_options = self._get_optimal_encoder()
            hw_info = f"使用硬件编码器: {hw_encoder}" if hw_encoder != "libx264" else "使用软件编码"
            self.update_progress(90, f"添加字幕...")
            self.logger.info(f"字幕添加使用编码器: {hw_encoder}")
            
            # 尝试复制字幕文件到临时目录，并为其创建唯一名称，以避免多线程冲突
            temp_dir = os.path.dirname(output_path)
            # 为临时字幕文件创建唯一名称
            unique_subtitle_name = f"temp_subtitle_{uuid.uuid4().hex[:8]}.srt"
            temp_subtitle_path = os.path.join(temp_dir, unique_subtitle_name)
            
            try:
                shutil.copy2(subtitle_path, temp_subtitle_path)
                self.logger.info(f"复制字幕文件到唯一的临时位置: {temp_subtitle_path}")
            except Exception as e:
                self.logger.error(f"致命错误：无法复制字幕到临时位置: {str(e)}，跳过字幕添加。")
                # 复制失败是严重问题，直接返回失败，避免使用错误的字幕
                shutil.copy2(input_video, output_path) # 复制未使用字幕的视频
                return False

            # 为FFmpeg滤镜正确转义路径 (关键修复)
            escaped_subtitle_path = self._escape_path_for_ffmpeg_filter(temp_subtitle_path)

            # 恢复原来可靠的编码参数
            if platform.system() == 'Windows':
                # 构建样式字符串
                style_str = (
                    f"Alignment={self._convert_position_to_alignment(subtitle_style.get('position', '底部'))},"
                )
                
                # 添加粗体和斜体设置
                if subtitle_style.get('bold', False):
                    style_str += "Bold=1,"
                else:
                    style_str += "Bold=0,"
                    
                if subtitle_style.get('italic', False):
                    style_str += "Italic=1,"
                else:
                    style_str += "Italic=0,"
                
                # 添加其他样式设置
                style_str += (
                    f"OutlineColour={self._convert_color_to_ffmpeg(subtitle_style.get('outline_color', '黑色'))},"
                    f"BorderStyle=1,"
                    f"Outline={subtitle_style.get('outline_width', 1.0)},"
                    f"Shadow=0,"
                    f"Fontsize={subtitle_style.get('font_size', 22)},"
                    f"FontName={subtitle_style.get('font', '微软雅黑')},"
                    f"PrimaryColour={self._convert_color_to_ffmpeg(subtitle_style.get('font_color', '白色'))}"
                )
                
                # 记录样式字符串用于调试
                self.logger.info(f"使用字幕样式: {style_str}")
                
                # 恢复原来稳定工作的编码选项，但使用自定义码率
                encoder_options = ""
                if hw_encoder == "h264_nvenc":
                    # NVIDIA GPU稳定参数 - 使用fast预设提高速度
                    encoder_options = f"-c:v {hw_encoder} -preset p1 -rc vbr -crf 30 -b:v {bitrate}"
                elif hw_encoder == "h264_qsv":
                    # Intel QSV稳定参数 - 使用较快的预设
                    encoder_options = f"-c:v {hw_encoder} -preset medium -global_quality 27 -b:v {bitrate}"
                elif hw_encoder == "h264_amf":
                    # AMD AMF稳定参数 - 简化AMD参数设置，保留原有比特率
                    encoder_options = f"-c:v {hw_encoder} -quality speed -usage transcoding -b:v {bitrate}"
                else:
                    # 软件编码稳定参数 - 使用更快的预设
                    encoder_options = f"-c:v libx264 -preset medium -crf 30 -b:v {bitrate}"
                
                # 构建命令 - 使用包含完整路径的字幕文件，不再依赖os.chdir
                cmd = (
                    f'cmd /c ffmpeg -y -i "{input_video}" -threads 0 -lavfi "subtitles={escaped_subtitle_path}:force_style='
                    f'\'{style_str}\'" {encoder_options} -c:a copy "{output_path}"'
                )
            
            self.logger.info(f"执行添加字幕命令: {cmd}")
            self.update_progress(91, f"执行字幕添加...")
            
            # 执行命令
            process = subprocess.Popen(
                cmd, 
                shell=True,
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace',
                startupinfo=self.startupinfo,
                creationflags=self.creation_flags,
                stdin=subprocess.DEVNULL
            )
            
            # 添加超时处理和进度显示
            start_time = time.time()
            last_update_time = start_time
            
            # 读取日志和进度，不设置超时
            while process.poll() is None:
                # 读取stderr获取进度信息
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        # 使用专门的方法提取并记录FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)
            
            # 获取结果
            result = process.wait()
            
            # 检查结果
            if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"字幕添加成功: {output_path}")
                self.update_progress(95, "字幕添加成功")
                return True
            else:
                self.logger.error(f"字幕添加失败，返回码: {result}")
                stderr_output = ""
                if process.stderr:
                    try:
                        stderr_output = process.stderr.read()
                    except:
                        pass
                
                if stderr_output:
                    self.logger.error(f"错误信息: {stderr_output}")
                
                # 尝试最简单的方式 - 使用-vf而不是-lavfi
                self.logger.info("尝试使用-vf替代-lavfi...")
                self.update_progress(92, "尝试备用字幕添加方法")
                
                # 直接使用-vf而不是-lavfi，仍使用硬件加速
                simple_cmd = (
                    f'cmd /c ffmpeg -y -i "{input_video}" -threads 0 -vf "subtitles={escaped_subtitle_path}" '
                    f'{encoder_options} -c:a copy "{output_path}"'
                )
                
                # 清理之前可能存在的输出文件
                if os.path.exists(output_path):
                    try:
                        os.remove(output_path)
                    except:
                        pass
                
                try:
                    self.logger.info(f"执行简单备用命令: {simple_cmd}")
                    # 使用Popen执行并显示进度
                    backup_process = subprocess.Popen(
                        simple_cmd, 
                        shell=True,
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags,
                        stdin=subprocess.DEVNULL
                    )
                    
                    # 读取进度
                    while backup_process.poll() is None:
                        if backup_process.stderr:
                            line = backup_process.stderr.readline()
                            if line:
                                self._extract_and_log_ffmpeg_progress(line.strip())
                            else:
                                time.sleep(0.1)
                        else:
                            time.sleep(0.1)

                    backup_result = backup_process.wait()

                    if backup_result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                        self.logger.info(f"备用字幕添加方法成功: {output_path}")
                        self.update_progress(95, "字幕添加成功")
                        return True
                    else:
                        self.logger.error(f"备用字幕添加方法失败，返回码: {backup_result}")
                        if os.path.exists(input_video):
                             shutil.copy2(input_video, output_path)
                        return False

                except Exception as backup_e:
                    self.logger.error(f"备用字幕方法执行时出错: {str(backup_e)}")
                    if os.path.exists(input_video):
                        shutil.copy2(input_video, output_path)
                    return False
        finally:
            # 清理临时字幕文件
            try:
                if temp_subtitle_path and os.path.exists(temp_subtitle_path):
                    os.remove(temp_subtitle_path)
                    self.logger.info(f"已清理临时字幕文件: {temp_subtitle_path}")
            except Exception as e:
                self.logger.warning(f"清理临时字幕文件时出错: {str(e)}")

    def _escape_path_for_ffmpeg_filter(self, path):
        """为FFmpeg的-vf/-lavfi滤镜转义Windows路径。"""
        if platform.system() == "Windows":
            # 1. 将所有反斜杠替换为正斜杠
            # 2. 对冒号进行转义
            return path.replace('\\', '/').replace(':', '\\\\:')
        return path

    def _execute_process_command(self, cmd):
        """执行一个子进程命令，并实时记录其输出，处理编码问题"""
        self.logger.info(f"准备执行命令: {cmd}")
        
        try:
            # 使用预先初始化的startupinfo
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    cmd, 
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE, 
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    stdin=subprocess.DEVNULL  # 添加stdin=DEVNULL防止控制台交互
                )
            else:
                process = subprocess.Popen(
                    cmd, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE, 
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 取消超时处理，无限等待直到处理完成
            start_time = time.time()
            last_update_time = start_time
            
            # 实时获取处理进度信息
            while process.poll() is None:
                # 获取输出
                if process.stderr:
                    try:
                        # 直接读取一行，替换select.select调用
                        line = process.stderr.readline()
                        if line:
                            last_update_time = time.time()
                            # 使用专门的方法提取并记录FFmpeg进度
                            if self._extract_and_log_ffmpeg_progress(line.strip()):
                                # 进度已处理，无需进一步操作
                                pass
                        else:
                            # 没有新数据
                            time.sleep(0.1)
                    except Exception as e:
                        self.logger.warning(f"读取进度时出错: {str(e)}")
                        time.sleep(0.5)
                else:
                    time.sleep(0.5)  # 如果没有stderr，短暂休眠避免CPU占用过高
                
                # 更新处理时间显示
                current_time = time.time()
                if current_time - last_update_time > 5.0:
                    elapsed_time = current_time - start_time
                    elapsed_str = self._format_time(elapsed_time)
                    self.update_progress(None, f"处理中... 已耗时: {elapsed_str}")
                    last_update_time = current_time
            
            # 如果进程被我们终止了，视为失败
            if process.returncode == -9:  # 被kill信号终止
                self.logger.error("视频处理进程被强制终止")
                raise Exception("视频处理超时或无响应，已强制终止")
                
            result = process.wait()
            
            if result != 0:
                stderr_output = ""
                if process.stderr:
                    try:
                        stderr_output = "".join(process.stderr.readlines())
                    except:
                        pass
                
                self.logger.error(f"视频处理失败: {stderr_output}")
                raise Exception(f"处理音频和视频失败: {stderr_output}")
            
            self.logger.info("视频处理成功")
            return True
            
        except Exception as e:
            self.logger.error(f"视频处理错误: {str(e)}")
            raise Exception(f"处理失败: {str(e)}")
    def _execute_simple_command(self, cmd):
        """执行简单命令，不需要显示进度"""
        try:
            if isinstance(cmd, list):
                cmd_str = " ".join(cmd)
            else:
                cmd_str = cmd
                
            self.logger.info(f"执行命令: {cmd_str}")
            
            # 使用预先初始化的startupinfo
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    cmd, 
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    encoding='utf-8',
                    errors='replace',
                    stdin=subprocess.DEVNULL  # 添加stdin参数防止控制台交互
                )
            else:
                process = subprocess.Popen(
                    cmd, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 等待处理完成，不设置超时
            stdout, stderr = process.communicate()
            return process.returncode == 0
                
        except Exception as e:
            self.logger.error(f"命令执行错误: {str(e)}")
            return False
    
    def _format_time(self, seconds):
        """将秒数格式化为时:分:秒格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
    
    def _execute_merge_with_progress(self, cmd_str, output_path):
        """执行合并命令并显示进度"""
        try:
            start_time = time.time()
            last_update_time = start_time
            
            # 使用预先初始化的startupinfo
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    cmd_str, 
                    shell=True,
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    bufsize=1,
                    encoding='utf-8',
                    errors='replace',
                    stdin=subprocess.DEVNULL  # 添加stdin参数
                )
            else:
                process = subprocess.Popen(
                    cmd_str, 
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    bufsize=1,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 读取输出并更新进度
            progress_pattern = re.compile(r'time=(\d+):(\d+):(\d+.\d+)')
            duration_pattern = re.compile(r'Duration: (\d+):(\d+):(\d+.\d+)')
            error_output = []
            duration_seconds = 0
            
            self.update_progress(80, "正在合并视频...")
            
            while True:
                # 检查进程是否还在运行
                if process.poll() is not None:
                    break
                
                # 读取输出，确保stderr不为None
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        line = line.strip()
                        error_output.append(line)
                        
                        # 使用统一的方法处理FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line)
                        
                        # 查找时长信息
                        if duration_seconds == 0:
                            duration_match = duration_pattern.search(line)
                            if duration_match:
                                h, m, s = duration_match.groups()
                                duration_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                                self.logger.info(f"检测到视频总时长: {duration_seconds}秒")
                        
                        # 查找并更新进度百分比
                        match = progress_pattern.search(line)
                        if match and duration_seconds > 0:
                            h, m, s = match.groups()
                            current_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                            progress_percent = min(current_seconds / duration_seconds * 100, 100)
                            
                            # 计算当前进度（基础进度 + 合并进度的比例）
                            current_progress = 80 + min(progress_percent / 5, 19)  # 80-99的进度范围
                            
                            # 仅更新进度条，不重复输出FFmpeg信息
                            current_time = time.time()
                            if current_time - last_update_time >= 1.0:  # 每1秒更新一次进度百分比
                                self.update_progress(int(current_progress), "")
                                last_update_time = current_time
                
                # 防止CPU过度使用
                time.sleep(0.1)
            
            # 检查进程返回码
            if process.returncode != 0:
                self.logger.error(f"合并失败，FFmpeg返回码: {process.returncode}")
                if error_output:
                    self.logger.error(f"FFmpeg错误输出: {', '.join(error_output[-10:])}")
                return False
            
            # 验证输出文件是否生成成功
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                self.logger.error(f"合并后的文件不存在或为空")
                return False
            
            self.logger.info(f"合并成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"合并时发生错误: {str(e)}")
            traceback.print_exc()
            return False
    
    def _get_video_info(self, video_path):
        """获取视频信息，包括时长等"""
        try:
            cmd = [
                "ffprobe",
                "-v", "error",
                "-show_entries", "format=duration,size",
                "-show_entries", "stream=width,height,codec_name",
                "-of", "json",
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
            
            if result.returncode != 0:
                self.logger.warning(f"获取视频信息失败，返回码: {result.returncode}")
                self.logger.warning(f"错误信息: {result.stderr}")
                return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}
            
            info = json.loads(result.stdout)
            
            # 提取所需信息
            video_info = {}
            
            # 提取格式信息
            if 'format' in info:
                video_info['duration'] = info['format'].get('duration', '0')
                video_info['size'] = info['format'].get('size', '0')
            
            # 提取流信息（找到第一个视频流）
            if 'streams' in info:
                for stream in info['streams']:
                    if stream.get('codec_type') == 'video':
                        video_info['width'] = stream.get('width', '0')
                        video_info['height'] = stream.get('height', '0')
                        video_info['codec_name'] = stream.get('codec_name', 'unknown')
                        break
            
            return video_info
        except Exception as e:
            self.logger.error(f"获取视频信息时出错: {str(e)}")
            return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}
            
    def _get_random_bgm_file(self, folder_path):
        """从BGM文件夹中随机选择一个音频文件，避免重复选择"""
        if not os.path.isdir(folder_path):
            self.logger.error(f"BGM路径不是文件夹: {folder_path}")
            return None
        
        # 获取所有支持的音频文件
        audio_files = []
        for ext in ['.mp3', '.wav', '.flac', '.m4a', '.aac']:
            audio_files.extend(glob.glob(os.path.join(folder_path, f"*{ext}")))
            audio_files.extend(glob.glob(os.path.join(folder_path, f"*{ext.upper()}")))
        
        if not audio_files:
            self.logger.error(f"BGM文件夹中没有找到音频文件: {folder_path}")
            return None
            
        # 初始化已使用的BGM记录列表
        if not hasattr(self, '_used_bgm_files'):
            self._used_bgm_files = []
            
        # 获取未使用的BGM文件
        unused_files = [f for f in audio_files if f not in self._used_bgm_files]
        
        # 如果所有文件都已使用过，则重置已使用列表
        if not unused_files:
            self.logger.info("所有BGM文件已使用过，重置使用记录")
            self._used_bgm_files = []
            unused_files = audio_files
        
        # 随机选择一个未使用的文件
        random_bgm = random.choice(unused_files)
        
        # 记录已使用的文件
        self._used_bgm_files.append(random_bgm)
        
        self.logger.info(f"从文件夹中随机选择BGM: {os.path.basename(random_bgm)}")
        return random_bgm
    
    def merge_original_with_first_video(self, subfolder, output_dir):
        """合并原视频与1号音频处理视频 - 将1号视频拼接在原视频之后"""
        try:
            # 直接在子文件夹中查找名为"原视频"的文件
            # 支持常见的视频扩展名
            video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')
            original_video = None
            
            # 遍历子文件夹中的所有文件
            for file in os.listdir(subfolder):
                # 检查文件名是否为"原视频"（不区分扩展名）
                file_name_without_ext = os.path.splitext(file)[0]
                if file_name_without_ext == "原视频" and file.lower().endswith(video_extensions):
                    original_video = os.path.join(subfolder, file)
                    break
            
            # 检查是否找到了原视频
            if not original_video:
                self.logger.warning(f"在子文件夹中未找到名为'原视频'的视频文件: {subfolder}")
                return None
                
            original_video_name = "原视频"
            self.logger.info(f"找到原视频: {original_video}")
            
            # 查找处理后的"1"视频
            processed_video_name = "1.mp4"
            processed_video = os.path.join(output_dir, processed_video_name)
            
            # 检查1.mp4是否存在
            if not os.path.exists(processed_video):
                self.logger.warning(f"未找到1号音频处理视频: {processed_video}")
                return None
                
            self.logger.info(f"找到1号音频处理视频: {processed_video}")
            
            # 创建合并视频的输出路径 - 使用更有意义的名称
            output_path = os.path.join(output_dir, f"{original_video_name}_合并1号视频.mp4")
            
            # 创建临时目录用于存放中间文件
            temp_dir = os.path.join(output_dir, f"temp_{int(time.time())}")
            os.makedirs(temp_dir, exist_ok=True)
            self.temp_dirs.append(temp_dir)  # 添加到临时目录列表以便后续清理
            
            # 全新改进的解决方案：单独处理原视频和处理视频，确保统一参数
            self.logger.info("使用改进的三步同步合并方法")
            
            # 获取自定义码率
            custom_bitrate = "2000k"
            
            # 检查自定义码率设置
            if 'custom_bitrate' in self.options and self.options['custom_bitrate']:
                bitrate_value = self.options.get('bitrate', '2000')
                # 如果码率值大于1000，使用M单位，否则使用k单位
                if int(bitrate_value) >= 1000:
                    custom_bitrate = f"{int(bitrate_value)//1000}M"
                else:
                    custom_bitrate = f"{bitrate_value}k"
                self.logger.info(f"使用自定义码率: {custom_bitrate}")
            
            # 步骤1: 处理原视频，确保时间戳和关键帧正确
            self.logger.info("步骤1: 处理原视频")
            self.update_progress(70, "处理原视频...")
            
            # 获取处理视频的信息，以确保原视频与之匹配
            proc_info = self._get_video_info(processed_video)
            proc_fps = proc_info.get('fps', '30')
            
            # 获取分辨率设置
            resolution_width = "1280"
            resolution_height = "720"
            if self.options.get('custom_resolution', False):
                resolution_width = self.options.get('resolution_width', '1280')
                resolution_height = self.options.get('resolution_height', '720')
                self.logger.info(f"使用自定义分辨率: {resolution_width}x{resolution_height}")
            
            video_scale = f"{resolution_width}:{resolution_height}"
                
            proc_sample_rate = proc_info.get('sample_rate', '44100')
            
            # 处理原视频
            self.logger.info("处理原视频...")
            self.update_progress(70, "处理原视频...")
            
            original_processed = os.path.join(temp_dir, "original_processed.mp4")
            
            # 检测可用的硬件加速
            has_nvidia = self.check_nvidia_gpu()
            has_intel = self.check_intel_gpu()
            has_amd = self.check_amd_gpu()
            
            # 构建原视频处理命令
            original_cmd = None
            if has_nvidia:
                original_cmd = [
                    "ffmpeg", "-y",
                    "-i", original_video,  # 修改这里，使用原视频而不是处理后的视频
                    "-c:v", "h264_nvenc",
                    "-preset", "p2",
                    "-b:v", custom_bitrate,
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-pix_fmt", "yuv420p",
                    "-strict", "experimental",
                    "-gpu", "0",
                    original_processed
                ]
                self.logger.info("使用NVIDIA GPU处理原视频")
            elif has_intel:
                original_cmd = [
                    "ffmpeg", "-y",
                    "-i", original_video,
                    "-c:v", "h264_qsv",
                    "-preset", "medium",
                    "-b:v", custom_bitrate,
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-pix_fmt", "nv12",
                    "-strict", "experimental",
                    original_processed
                ]
                self.logger.info("使用Intel GPU处理原视频")
            elif has_amd:
                original_cmd = [
                    "ffmpeg", "-y",
                    "-i", original_video,
                    "-c:v", "h264_amf",
                    "-quality", "speed",        # 速度优先模式
                    "-b:v", custom_bitrate,
                    "-usage", "transcoding",    # 使用转码模式
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-strict", "experimental",
                    original_processed
                ]
                self.logger.info("使用AMD GPU处理原视频")
            else:
                original_cmd = [
                    "ffmpeg", "-y",
                    "-i", original_video,
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "30",
                    "-b:v", custom_bitrate,
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-strict", "experimental",
                    "-threads", "0",
                    original_processed
                ]
                self.logger.info("使用CPU软件编码处理原视频")
            
            self.logger.info(f"原视频处理命令: {' '.join(original_cmd)}")
            
            # 执行原视频处理
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    original_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1,
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                 process = subprocess.Popen(
                    original_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1
                )
            
            self._monitor_ffmpeg_progress(process, "处理原视频", 70)
            result = process.wait()
            
            
            # 步骤2: 处理1号视频，确保统一格式
            self.logger.info("步骤2: 处理1号视频")
            self.update_progress(80, "处理1号视频...")
            
            processed_processed = os.path.join(temp_dir, "processed_processed.mp4")
            
            # 对处理视频也执行相同的参数处理，使用相同的硬件加速
            if has_nvidia:
                # NVIDIA GPU加速
                processed_cmd = [
                    "ffmpeg", "-y",
                    "-i", processed_video,
                    "-c:v", "h264_nvenc",
                    "-preset", "p2",
                    "-b:v", "3000k",
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-pix_fmt", "yuv420p",
                    "-strict", "experimental",
                    "-gpu", "0",
                    processed_processed
                ]
                self.logger.info("使用NVIDIA GPU加速处理1号视频")
            elif has_intel:
                # Intel GPU加速
                processed_cmd = [
                    "ffmpeg", "-y",
                    "-i", processed_video,
                    "-c:v", "h264_qsv",
                    "-preset", "medium",
                    "-b:v", "3000k",
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-pix_fmt", "nv12",
                    "-strict", "experimental",
                    processed_processed
                ]
                self.logger.info("使用Intel GPU加速处理1号视频")
            elif has_amd:
                # AMD GPU加速
                processed_cmd = [
                    "ffmpeg", "-y",
                    "-i", processed_video,
                    "-c:v", "h264_amf",
                    "-quality", "speed",        # 速度优先模式
                    "-b:v", custom_bitrate,
                    "-usage", "transcoding",    # 使用转码模式
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-strict", "experimental",
                    processed_processed
                ]
                self.logger.info("使用AMD GPU加速处理1号视频")
            else:
                # 软件编码
                processed_cmd = [
                    "ffmpeg", "-y",
                    "-i", processed_video,
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "30",
                    "-r", proc_fps,
                    "-vf", f"scale={video_scale}",
                    "-vsync", "1",
                    "-sn",
                    "-c:a", "aac",
                    "-b:a", "192k",
                    "-ar", proc_sample_rate,
                    "-ac", "2",
                    "-pix_fmt", "yuv420p",
                    "-strict", "experimental",
                    "-threads", "0",
                    processed_processed
                ]
                self.logger.info("使用CPU软件编码处理1号视频")
            
            self.logger.info(f"处理1号视频: {' '.join(processed_cmd)}")
            
            # 执行命令并监控进度
            if platform.system() == 'Windows':
                process2 = subprocess.Popen(
                    processed_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1,
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                 process2 = subprocess.Popen(
                    processed_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1
                )
            
            self._monitor_ffmpeg_progress(process2, "处理1号视频", 80)
            
            # 获取返回码
            result2 = process2.wait()
            

            # 步骤3: 使用concat demuxer合并视频（比filter更可靠）
            self.logger.info("步骤3: 合并处理后的视频")
            self.update_progress(90, "合并视频...")
            
            # 创建concat文件
            concat_file = os.path.join(temp_dir, "concat.txt")
            with open(concat_file, 'w', encoding='utf-8') as f:
                original_path = original_processed.replace('\\', '/')
                processed_path = processed_processed.replace('\\', '/')
                f.write(f"file '{original_path}'\n")
                f.write(f"file '{processed_path}'\n")
            
            # 使用concat demuxer合并视频（更可靠）
            merge_cmd = [
                "ffmpeg", "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", concat_file,
                "-c", "copy",             # 直接复制流，因为已经预处理过了
                "-movflags", "+faststart",
                output_path
            ]
            
            self.logger.info(f"合并视频: {' '.join(merge_cmd)}")
            
            # 执行命令并监控进度
            if platform.system() == 'Windows':
                process3 = subprocess.Popen(
                    merge_cmd,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1,
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                process3 = subprocess.Popen(
                    merge_cmd,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    bufsize=1
                )
            
            self._monitor_ffmpeg_progress(process3, "合并视频", 90)
            
            # 获取返回码
            result3 = process3.wait()
            
            # 验证最终输出
            if result3 == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 1024 * 1024:
                if self._verify_output_video(output_path):
                    self.logger.info(f"成功合并原视频与1号视频: {output_path}")
                    self.update_progress(100, "视频合并完成")
                    return output_path
            
            # 如果合并仍然失败，尝试使用单独的命令
            self.logger.warning("合并失败，尝试单独合并命令...")
            
            # 最后的尝试：使用单独命令
            final_cmd = [
                "ffmpeg", "-y",
                "-i", original_processed,
                "-i", processed_processed,
                "-filter_complex", 
                "[0:v][0:a][1:v][1:a] concat=n=2:v=1:a=1 [v] [a]",
                "-map", "[v]",
                "-map", "[a]",
                "-c:v", "libx264",
                "-preset", "ultrafast",
                "-crf", "30",
                "-c:a", "aac",
                "-b:a", "192k",
                "-movflags", "+faststart",
                output_path
            ]
            
            self.logger.info(f"最终尝试: {' '.join(final_cmd)}")
            
            # 执行命令
            if platform.system() == 'Windows':
                final_result = subprocess.run(final_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:
                final_result = subprocess.run(final_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            if final_result.returncode == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 1024 * 1024:
                if self._verify_output_video(output_path):
                    self.logger.info("最终尝试成功合并视频")
                    return output_path
            
        except Exception as e:
            self.logger.error(f"合并原视频与1号视频时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    def _monitor_ffmpeg_progress(self, process, task_name, progress_base):
        """监控ffmpeg进度并更新UI"""
        # 正则表达式用于提取进度信息
        progress_regex = re.compile(r'time=(\d+:\d+:\d+\.\d+).*bitrate=\s*(\d+\.\d+)kbits/s.*speed=\s*(\d+\.\d+)x')
        frame_regex = re.compile(r'frame=\s*(\d+)\s*fps=\s*(\d+\.\d+)')
        
        while process.poll() is None:
            # 读取stderr获取进度信息
            if process.stderr:
                line = process.stderr.readline().strip()
                if not line:
                    time.sleep(0.1)
                    continue
                    
                # 记录所有ffmpeg输出到日志
                self.logger.debug(f"ffmpeg输出: {line}")
                
                # 提取时间、比特率、速度等信息
                progress_match = progress_regex.search(line)
                if progress_match:
                    time_str, bitrate, speed = progress_match.groups()
                    frame_info = ""
                    frame_match = frame_regex.search(line)
                    if frame_match:
                        frame, fps = frame_match.groups()
                        frame_info = f" frame={frame} fps={fps}"
                    
                    # 打印详细进度信息
                    progress_info = f"time={time_str} bitrate={bitrate}kbits/s speed={speed}x{frame_info}"
                    self.logger.info(f"【进度】{task_name}: {progress_info}")
                    # 更新UI进度信息
                    self.update_progress(progress_base, f"{task_name}: {progress_info}")
            else:
                time.sleep(0.1)
    
    
    def _verify_output_video(self, output_path):
        """验证输出视频是否可播放"""
        try:
            check_cmd = [
                "ffprobe", "-v", "error", 
                "-select_streams", "v:0", 
                "-show_entries", "stream=codec_type", 
                "-of", "csv=p=0", 
                output_path
            ]
            if platform.system() == 'Windows':
                result = subprocess.run(check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:
                result = subprocess.run(check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            if result.returncode == 0 and "video" in result.stdout.lower():
                return True
            return False
        except Exception as e:
            self.logger.warning(f"验证视频失败: {str(e)}")
            return False
    


    
    # 辅助方法：将颜色名称转换为FFmpeg颜色代码
    def _convert_color_to_ffmpeg(self, color_name):
        """将中文颜色名称转换为FFmpeg可用的十六进制颜色代码"""
        color_map = {
            "白色": "&HFFFFFF",
            "黑色": "&H000000",
            "红色": "&H0000FF",
            "绿色": "&H00FF00",
            "蓝色": "&HFF0000",
            "黄色": "&H00D7FF",  # 从 &H00FFFF (亮黄色) 改为 &H00D7FF (金色)
            "青色": "&HFFFF00",
            "洋红": "&HFF00FF"
        }
        return color_map.get(color_name, '&H00FFFFFF')  # 默认返回白色
        
    # 辅助方法：将位置名称转换为ASS文件中的alignment值
    def _convert_position_to_alignment(self, position):
        """将位置名称转换为ASS文件中的alignment值（数字1-9）"""
        position_map = {
            '左上角': 7,
            '顶部': 8, 
            '右上角': 9,
            '左侧': 4,
            '中间': 10,
            '右侧': 6,
            '左下角': 1,
            '底部': 2,
            '右下角': 3
        }
        return position_map.get(position, 2)  # 默认返回底部中心
    
    def merge_videos(self, video_files, output_path):
        """合并多个视频文件为一个视频。
        简化版本：直接使用ffmpeg的concat demuxer和copy模式合并视频
        
        Args:
            video_files: 要合并的视频文件路径列表
            output_path: 输出文件路径
        
        Returns:
            bool: 合并是否成功
        """
        if not video_files or len(video_files) < 2:
            self.logger.error("需要至少两个视频文件进行合并")
            return False
            
        self.logger.info(f"开始合并视频: {', '.join(video_files)} 到 {output_path}")
        total_videos = len(video_files)
        self.update_progress(60, f"准备合并 {total_videos} 个视频...")
        
        # 创建临时目录在输出文件所在目录下
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # 创建临时目录
        target_temp_dir = os.path.join(output_dir, f"临时_{int(time.time())}")
        os.makedirs(target_temp_dir, exist_ok=True)
        self.temp_dirs.append(target_temp_dir)  # 添加到临时目录列表中以便后续清理
        
        self.logger.info(f"创建临时目录: {target_temp_dir}")
        
        try:
            # 检查视频文件是否存在
            valid_videos = []
            for i, video in enumerate(video_files):
                if os.path.exists(video) and os.path.getsize(video) > 0:
                    valid_videos.append(video)
                    # 更新检查进度
                    self.update_progress(60 + int((i+1) / total_videos * 20), f"验证视频文件 {i+1}/{total_videos}...")
                else:
                    self.logger.error(f"视频文件不存在或为空: {video}")
            
            if len(valid_videos) < 2:
                self.logger.error("没有足够的有效视频进行合并")
                return False
            
            # 创建文件列表，使用原始视频
            self.logger.info(f"创建视频列表，共 {len(valid_videos)} 个视频")
            list_file_path = os.path.join(target_temp_dir, "merge_list.txt")
            
            with open(list_file_path, 'w', encoding='utf-8') as f:
                for i, video in enumerate(valid_videos):
                    # 确保路径格式正确
                    video_path = video.replace('\\', '/')
                    f.write(f"file '{video_path}'\n")
                    # 更新写入进度
                    self.update_progress(80 + int((i+1) / len(valid_videos) * 10), 
                                       f"准备合并列表 {i+1}/{len(valid_videos)}...")
            
            # 直接使用copy模式合并视频
            self.logger.info("使用简化的copy模式合并视频...")
            self.update_progress(90, f"开始合并 {len(valid_videos)} 个视频...")
            
            # 构建合并命令 - 使用copy模式，不重新编码
            merge_cmd = [
                "ffmpeg", "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", f'"{list_file_path}"',
                "-c", "copy",  # 直接复制流，不重新编码
                "-max_muxing_queue_size", "9999",
                "-movflags", "+faststart",
                f'"{output_path}"'
            ]
            
            merge_cmd_str = " ".join(merge_cmd)
            if platform.system() == 'Windows':
                merge_cmd_str = f'cmd /c {merge_cmd_str}'
            
            self.logger.info(f"执行合并命令: {merge_cmd_str}")
            
            # 执行合并命令
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    merge_cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                process = subprocess.Popen(
                    merge_cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 读取进度
            start_time = time.time()
            while process.poll() is None:
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        # 记录关键信息到日志
                        if "Non-monotonous" in line or "timestamp" in line:
                            self.logger.warning(f"时间戳警告: {line.strip()}")
                        if "error" in line.lower() or "could not" in line.lower():
                            self.logger.warning(f"错误信息: {line.strip()}")
                        
                        # 使用专门的方法提取并记录FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line.strip())
                else:
                    time.sleep(0.1)
            
            # 获取结果
            result = process.wait()
            
            # 检查结果
            if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"视频合并成功: {output_path}")
                self.update_progress(100, f"成功合并 {len(valid_videos)} 个视频")
                return True
            else:
                self.logger.error(f"视频合并失败，返回码: {result}")
                stderr_output = ""
                if process.stderr:
                    try:
                        stderr_output = process.stderr.read()
                    except:
                        pass
                
                if stderr_output:
                    self.logger.error(f"错误信息: {stderr_output}")
                return False
                
        except Exception as e:
            self.logger.error(f"合并视频时出错: {str(e)}")
            traceback.print_exc()
            return False
            
        finally:
            # 临时目录清理由self.temp_dirs处理
            pass

    # 添加缺失的方法
    def _get_optimal_encoder(self):
        """获取最佳编码器配置，优化GPU利用率"""
        # 获取自定义码率
        bitrate = "2000k"
        if self.options.get('custom_bitrate', False):
            bitrate_value = self.options.get('bitrate', '2000')
            # 如果码率值大于1000，使用M单位，否则使用k单位
            if int(bitrate_value) >= 1000:
                bitrate = f"{int(bitrate_value)//1000}M"
            else:
                bitrate = f"{bitrate_value}k"
            self.logger.info(f"在编码器设置中使用自定义码率: {bitrate}")
            
        # 检测系统可用的硬件加速
        if self.options.get('hardware_acceleration', False):
            system_info = platform.system().lower()
            
            # Windows平台上的硬件支持
            if system_info == "windows":
                if self.check_nvidia_gpu():
                    self.logger.info("检测到NVIDIA GPU，使用NVENC硬件加速")
                    return "h264_nvenc", [
                "-preset", "p1",  # 最快的预设
                "-profile:v", "high",
                "-rc", "vbr",
                "-cq", "30",  # 降低质量限制以提高速度
                "-qmin", "15", # 降低最小量化参数
                "-qmax", "28",
                "-b:v", bitrate,   # 使用自定义比特率
                #"-bf", "4",     # 增加B帧数量
               # "-spatial_aq", "1", # 启用空间自适应量化
               # "-temporal_aq", "1", # 启用时间自适应量化
                "-nonref_p", "1", # 使用非参考P帧
                "-strict_gop", "1", # 严格的GOP结构
               # "-b_ref_mode", "middle", # B帧参考模式
                #"-multipass", "fullres"  # 多通道编码
                    ]
                elif self.check_intel_gpu():
                    self.logger.info("检测到Intel GPU，使用QSV硬件加速")
                    return "h264_qsv", [
                        "-preset", "medium",
                        "-profile:v", "high",
                        "-global_quality", "23", 
                        "-look_ahead", "0", 
                        "-b:v", bitrate,  
                        "-low_power", "0", 
                        "-num_ref_frame", "5", 
                        "-g", "50",  # 关键帧间隔
                        "-threads", "0",  # 自动线程数
                        "-thread_queue_size", "2048"  # 增加队列大小
                    ]
                elif self.check_amd_gpu():
                    self.logger.info("检测到AMD GPU，使用AMF硬件加速")
                    return "h264_amf", [
                        "-quality", "speed",        # 速度优先模式
                        "-b:v", bitrate,            # 使用自定义比特率
                        "-usage", "transcoding",    # 使用转码模式
                        "-threads", "0",            # 自动线程数
                        "-thread_queue_size", "2048" # 增加队列大小
                    ]
            # Linux平台上的硬件支持
            elif system_info == "linux":
                if self.check_nvidia_gpu():
                    self.logger.info("检测到NVIDIA GPU，使用NVENC硬件加速")
                    return "h264_nvenc", [
                        "-preset", "p5", 
                        "-rc", "vbr", 
                        "-crf", "30", 
                        "-b:v", bitrate,
                        "-threads", "0",  # 自动线程数
                        "-thread_queue_size", "2048"  # 增加队列大小
                    ]
                elif self.check_vaapi():
                    self.logger.info("检测到VAAPI，使用VAAPI硬件加速")
                    return "h264_vaapi", [
                        "-vaapi_device", "/dev/dri/renderD128", 
                        "-global_quality", "27", 
                        "-b:v", bitrate,
                        "-threads", "0",  # 自动线程数
                        "-thread_queue_size", "2048"  # 增加队列大小
                    ]
            # macOS平台上的硬件支持
            elif system_info == "darwin":
                self.logger.info("在macOS上使用VideoToolbox硬件加速")
                return "h264_videotoolbox", [
                    "-q", "50", 
                    "-b:v", bitrate,
                    "-threads", "0",  # 自动线程数
                    "-thread_queue_size", "2048"  # 增加队列大小
                ]
        
        # 默认使用软件编码
        self.logger.info("使用软件编码器")
        return "libx264", [
            "-preset", "medium", # 使用更快的预设
            "-profile:v", "high",
            "-crf", "30", # 略微降低质量以提高速度
            "-pix_fmt", "yuv420p",
            "-b:v", bitrate,  # 使用自定义比特率
            "-tune", "fastdecode", # 优化快速解码
            "-direct-pred", "spatial", # 空间预测
            "-partitions", "i8x8,i4x4", # 限制分区类型
            "-refs", "3", # 减少参考帧数量
            "-subq", "4", # 降低子像素运动估计质量
            "-trellis", "0", # 禁用trellis量化
            "-weightb", "0", # 禁用加权B帧预测
            "-threads", "0"  # 自动线程数
        ]

    def _execute_merge_command(self, cmd_str, output_path, processed_videos):
        """执行合并命令并显示详细进度"""
        try:
            start_time = time.time()
            last_update_time = start_time

            
            process = subprocess.Popen(
                cmd_str, 
                shell=True,
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='replace'
            )
            
            # 读取输出并更新进度
            progress_pattern = re.compile(r'time=(\d+):(\d+):(\d+.\d+)')
            duration_pattern = re.compile(r'Duration: (\d+):(\d+):(\d+.\d+)')
            frame_pattern = re.compile(r'frame=\s*(\d+)')
            fps_pattern = re.compile(r'fps=\s*(\d+)')
            error_pattern = re.compile(r'Error|Invalid|Invalid\sargument|failed|Conversion\sfailed')
            error_output = []
            duration_seconds = 0
            last_frame = 0
            last_fps = 0
            frame_update_time = start_time
            processing_started = False
            
            self.update_progress(85, f"正在合并 {len(processed_videos)} 个视频...")
            
            while True:
 
                
                # 检查进程是否还在运行
                if process.poll() is not None:
                    break
                
                # 读取输出
                line = None
                if process.stderr:
                    line = process.stderr.readline()
                
                if line:
                    line = line.strip()
                    error_output.append(line)
                    
                    # 检测严重错误
                    if error_pattern.search(line):
                        self.logger.warning(f"检测到可能的错误: {line}")
                    
                    # 使用专门的方法提取并记录FFmpeg进度
                    if self._extract_and_log_ffmpeg_progress(line):
                        processing_started = True
                        last_update_time = time.time()
                    
                    # 查找时长信息
                    if duration_seconds == 0:
                        duration_match = duration_pattern.search(line)
                        if duration_match:
                            h, m, s = duration_match.groups()
                            duration_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                            self.logger.info(f"检测到视频总时长: {duration_seconds}秒")
                            processing_started = True
                    
                    # 查找当前处理时间点（仅用于进度条）
                    time_match = progress_pattern.search(line)
                    if time_match and duration_seconds > 0:
                        h, m, s = time_match.groups()
                        current_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                        
                        # 只计算和更新进度，不显示其他文本
                        progress = int(85 + (current_seconds / duration_seconds) * 14)  # 85-99的范围
                        self.update_progress(progress, "")
                
                # 处理长时间没有输出的情况
                current_time = time.time()
                if processing_started and current_time - last_update_time > 3.0:
                    elapsed_time = current_time - start_time
                    elapsed_str = self._format_time(elapsed_time)
                    self.update_progress(90, f"正在合并视频... 已处理: {elapsed_str}")
                    last_update_time = current_time
                elif not processing_started and current_time - last_update_time > 5.0:
                    # 处理启动阶段
                    elapsed_time = current_time - start_time
                    self.update_progress(85, f"正在启动合并处理... ({int(elapsed_time)}秒)")
                    last_update_time = current_time
                
                # 防止CPU过度使用
                time.sleep(0.1)
            
            # 检查进程返回码
            if process.returncode != 0:
                self.logger.error(f"合并失败，FFmpeg返回码: {process.returncode}")
                if error_output:
                    errors = '\n'.join(error_output[-20:])
                    self.logger.error(f"FFmpeg错误输出:\n{errors}")
                    # 详细记录所有错误信息，帮助调试
                    with open(os.path.join(os.path.dirname(output_path), "ffmpeg_error.log"), "w", encoding="utf-8") as f:
                        f.write("\n".join(error_output))
                return False
            
            # 验证输出文件是否生成成功
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                self.logger.error(f"合并后的文件不存在或为空")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"合并命令执行出错: {str(e)}")
            traceback.print_exc()
            return False
            
    def _add_bgm_to_video(self, input_video, output_path, bgm_path, volume=0.2):
        """添加背景音乐到视频，使用高性能方法"""
        self.logger.info(f"添加背景音乐: {os.path.basename(bgm_path)} 到 {os.path.basename(input_video)}")
        
        try:
            # 获取视频信息
            video_info = self._get_video_info(input_video)
            video_duration = float(video_info.get('duration', 0))
            
            if video_duration <= 0:
                self.logger.error("无法获取视频时长，无法添加背景音乐")
                return False
            
            # 检查BGM模式
            bgm_mode = self.options.get('bgm_mode', 'loop')
            self.logger.info(f"BGM模式: {bgm_mode}")
            
            # BGM路径处理
            if bgm_mode == 'random' and os.path.isdir(bgm_path):
                # 随机模式: 从文件夹中随机选择BGM文件
                bgm_concat_file = self._create_bgm_concat_file(bgm_path, video_duration, os.path.dirname(output_path))
                if not bgm_concat_file:
                    self.logger.error("创建BGM连接文件失败")
                    return False
                
                # 临时合并的BGM文件
                temp_bgm_file = os.path.join(os.path.dirname(output_path), f"temp_bgm_{int(time.time())}.mp3")
                
                # 规范化路径，全部使用正斜杠
                concat_file_path = bgm_concat_file.replace('\\', '/')
                temp_bgm_path = temp_bgm_file.replace('\\', '/')
                
                # 为ffmpeg命令构建安全的参数列表
                # 使用参数列表代替命令字符串，避免字符转义问题
                concat_cmd = [
                    "ffmpeg", "-y", 
                    "-f", "concat", 
                    "-safe", "0", 
                    "-i", concat_file_path, 
                    "-c", "copy", 
                    temp_bgm_path
                ]
                
                # 将列表转化为字符串，以便记录日志
                concat_cmd_str = " ".join(concat_cmd)
                self.logger.info(f"执行BGM合并命令: {concat_cmd_str}")
                
                try:
                    # 使用参数列表执行命令，避免shell解析
                    if platform.system() == 'Windows':
                        result = subprocess.run(
                            concat_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            encoding='utf-8',
                            errors='replace',
                            check=False,
                            startupinfo=self.startupinfo,
                            creationflags=self.creation_flags
                        )
                    else:
                        result = subprocess.run(
                            concat_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            encoding='utf-8',
                            errors='replace',
                            check=False
                        )
                    
                    # 记录输出，帮助调试
                    if result.stdout:
                        self.logger.debug(f"BGM合并标准输出: {result.stdout}")
                    if result.stderr:
                        self.logger.debug(f"BGM合并错误输出: {result.stderr}")
                    
                    if result.returncode != 0:
                        self.logger.error(f"BGM合并失败，返回码: {result.returncode}")
                        # 回退到使用单个BGM文件
                        self.logger.info("尝试直接使用单个BGM文件...")
                        if len(self._used_bgm_files) > 0:
                            bgm_file_to_use = self._used_bgm_files[-1]  # 使用最后选择的BGM文件
                            self.logger.info(f"使用单个BGM文件: {os.path.basename(bgm_file_to_use)}")
                        else:
                            random_bgm = self._get_random_bgm_file(bgm_path)
                            if random_bgm:
                                bgm_file_to_use = random_bgm
                                self.logger.info(f"随机选择单个BGM文件: {os.path.basename(bgm_file_to_use)}")
                            else:
                                self.logger.error("无法获取BGM文件")
                                return False
                    else:
                        # 检查文件是否真的创建了
                        if os.path.exists(temp_bgm_file) and os.path.getsize(temp_bgm_file) > 0:
                            self.logger.info(f"BGM合并成功: {temp_bgm_file}")
                            bgm_file_to_use = temp_bgm_file
                        else:
                            self.logger.error("BGM合并后文件不存在或为空")
                            # 回退到使用单个BGM文件
                            if len(self._used_bgm_files) > 0:
                                bgm_file_to_use = self._used_bgm_files[-1]
                                self.logger.info(f"使用单个BGM文件: {os.path.basename(bgm_file_to_use)}")
                            else:
                                self.logger.error("无法获取BGM文件")
                                return False
                except Exception as e:
                    self.logger.error(f"执行BGM合并命令时出错: {str(e)}")
                    # 回退到使用单个BGM文件
                    if len(self._used_bgm_files) > 0:
                        bgm_file_to_use = self._used_bgm_files[-1]
                        self.logger.info(f"使用单个BGM文件: {os.path.basename(bgm_file_to_use)}")
                    else:
                        random_bgm = self._get_random_bgm_file(bgm_path)
                        if random_bgm:
                            bgm_file_to_use = random_bgm
                            self.logger.info(f"随机选择单个BGM文件: {os.path.basename(bgm_file_to_use)}")
                        else:
                            self.logger.error("无法获取BGM文件")
                            return False
            else:
                # 循环模式: 使用单个BGM文件
                bgm_file_to_use = bgm_path
            
            # 获取视频和音频的时长信息
            try:
                bgm_info = self._get_video_info(bgm_file_to_use)
                bgm_duration = float(bgm_info.get('duration', 0))
                
                if bgm_duration <= 0:
                    self.logger.warning(f"无法获取BGM时长，将使用默认循环模式")
                    loop_count = -1
                else:
                    # 计算需要循环的次数，向上取整确保覆盖整个视频
                    loop_count = math.ceil(video_duration / bgm_duration)
                    self.logger.info(f"视频时长: {video_duration}秒, BGM时长: {bgm_duration}秒, 需要循环: {loop_count}次")
            except Exception as e:
                self.logger.warning(f"计算BGM循环次数时出错: {str(e)}，使用默认循环")
                loop_count = -1
            
            # 改进的方法：先将BGM重复拼接到足够长，然后再与视频混合
            # 使用更简单、可靠的方法，通过stream_loop参数循环BGM
            
            # 获取最佳编码器配置
            hw_encoder, hw_options = self._get_optimal_encoder()
            self.logger.info(f"添加BGM时使用视频直接复制模式，不进行重新编码")
            
            # 创建基本命令列表 - 使用copy模式直接复制视频流而不重新编码
            cmd_parts = [
                "ffmpeg", "-y",
                "-i", f'"{input_video}"',
                "-stream_loop", f"{loop_count}", 
                "-i", f'"{bgm_file_to_use}"',
                # 使音频混合方式，使用overlay模式，不混音
                "-filter_complex", f"[1:a]volume={volume}[bgm];[0:a][bgm]amix=inputs=2:duration=shortest:normalize=0[aout]",
                "-map", "0:v", 
                "-map", "[aout]",
                "-c:v", "copy",  # 直接复制视频流
                "-c:a", "libmp3lame", 
                "-q:a", "9",  # 最低质量(0-9范围，9为最低质量)
                "-compression_level", "0",  # 压缩级别
                "-shortest",
                "-max_muxing_queue_size", "4096",  # 队列大小
                # 添加高速处理特定参数
                "-fflags", "+genpts",  # 生成PTS
                "-flags", "+low_delay",  # 低延迟模式
                "-preset", "ultrafast",  # 音频使用最快预设
                "-tune", "zerolatency",  # 零延迟调优
                "-movflags", "+faststart",
                f'"{output_path}"'
            ]
            
            # 构建命令字符串，Windows系统下使用参数列表执行而不是shell命令
            if platform.system() == 'Windows':
                # 移除引号，直接使用参数列表方式执行
                clean_cmd = []
                for part in cmd_parts:
                    # 去除参数中的引号
                    clean_part = part.replace('"', '')
                    clean_cmd.append(clean_part)
                
                self.logger.info(f"使用参数列表方式执行BGM添加命令")
                
                # 使用subprocess.run直接执行命令列表，而不是通过shell
                try:
                    process = subprocess.Popen(
                        clean_cmd,
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                except Exception as e:
                    self.logger.error(f"启动BGM添加进程时出错: {str(e)}")
                    # 回退到shell模式
                    cmd_str = " ".join(cmd_parts)
                    self.logger.info(f"回退到shell模式执行: {cmd_str}")
                    cmd_str = f'cmd /c {cmd_str}'
                    process = subprocess.Popen(
                        cmd_str,
                        shell=True,
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        universal_newlines=True,
                        encoding='utf-8',
                        errors='replace',
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
            else:
                # 构建命令字符串
                cmd_str = " ".join(cmd_parts)
                self.logger.info(f"执行BGM添加命令: {cmd_str}")
                process = subprocess.Popen(
                    cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 添加进度显示，不设置超时
            start_time = time.time()
            last_update_time = start_time
            
            # 读取并处理输出
            while process.poll() is None:
                # 读取stderr获取进度信息
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        # 使用专门的方法提取并记录FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)
            
            # 读取剩余输出，确保不遗漏最后的进度信息
            if process.stderr:
                remaining_lines = process.stderr.readlines()
                for line in remaining_lines:
                    if line:
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    
            # 获取结果
            result = process.wait()
            
            # 清理临时文件
            try:
                if bgm_mode == 'random' and os.path.isdir(bgm_path):
                    if os.path.exists(temp_bgm_file):
                        os.remove(temp_bgm_file)
                        self.logger.info(f"清理临时BGM文件: {temp_bgm_file}")
                    if os.path.exists(bgm_concat_file):
                        os.remove(bgm_concat_file)
                        self.logger.info(f"清理BGM列表文件: {bgm_concat_file}")
            except Exception as e:
                self.logger.warning(f"清理临时BGM文件时出错: {str(e)}")
            
            # 检查结果
            if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"背景音乐添加成功: {os.path.basename(output_path)}")
                return True
            else:
                self.logger.error(f"背景音乐添加失败，ffmpeg返回码: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"添加背景音乐时出错: {str(e)}")
            traceback.print_exc()
            return False

    def _create_bgm_concat_file(self, bgm_folder, video_duration, temp_dir):
        """创建一个包含随机选择的多首BGM的文件列表，以填满视频时长"""
        self.logger.info(f"为视频（时长:{video_duration}秒）创建BGM列表")
        
        try:
            # 获取文件夹中的所有音频文件 - 只使用mp3、wav格式，避免格式兼容性问题
            audio_files = []
            for ext in ['.mp3', '.wav']:  # 仅使用最广泛兼容的格式
                audio_files.extend(glob.glob(os.path.join(bgm_folder, f"*{ext}")))
                audio_files.extend(glob.glob(os.path.join(bgm_folder, f"*{ext.upper()}")))
            
            # 如果mp3和wav文件太少，尝试使用其他格式
            if len(audio_files) < 3:
                for ext in ['.flac', '.m4a', '.aac']:
                    audio_files.extend(glob.glob(os.path.join(bgm_folder, f"*{ext}")))
                    audio_files.extend(glob.glob(os.path.join(bgm_folder, f"*{ext.upper()}")))
            
            if not audio_files:
                self.logger.error(f"BGM文件夹中没有找到音频文件: {bgm_folder}")
                return None
            
            # 创建文件列表文件
            list_file_path = os.path.join(temp_dir, f"bgm_list_{int(time.time())}.txt")
            
            # 初始化用于跟踪已选BGM和总时长的变量
            total_bgm_duration = 0.0
            selected_bgms = []
            available_bgms = audio_files.copy()
            
            # 初始化已使用的BGM记录列表（如果不存在）
            if not hasattr(self, '_used_bgm_files'):
                self._used_bgm_files = []
            
            # 更新可用BGM列表，移除最近使用过的BGM
            for bgm in self._used_bgm_files:
                if bgm in available_bgms:
                    available_bgms.remove(bgm)
            
            # 如果可用BGM数量不足，则重置
            if len(available_bgms) < 3 and len(audio_files) > 3:
                self.logger.info("可用的未使用BGM不足，重置已使用记录")
                self._used_bgm_files = []
                available_bgms = audio_files.copy()
            
            with open(list_file_path, 'w', encoding='utf-8') as f:
                # 随机选择BGM直到填满视频时长
                while total_bgm_duration < video_duration and (available_bgms or audio_files):
                    # 如果所有BGM都已使用过且仍需要更多BGM，则重置可用列表
                    if not available_bgms and audio_files:
                        self.logger.info("所有BGM已使用完毕，重置BGM列表继续填充")
                        available_bgms = audio_files.copy()
                        # 从已选择的BGM中移除最早的几首，以避免立即重复
                        for bgm in selected_bgms[-min(3, len(selected_bgms)):]:
                            if bgm in available_bgms:
                                available_bgms.remove(bgm)
                    
                    # 随机选择一首未使用的BGM
                    bgm_file = random.choice(available_bgms)
                    available_bgms.remove(bgm_file)
                    selected_bgms.append(bgm_file)
                    self._used_bgm_files.append(bgm_file)
                    
                    # 获取BGM时长
                    try:
                        bgm_info = self._get_video_info(bgm_file)
                        bgm_duration = float(bgm_info.get('duration', 0))
                        
                        if bgm_duration <= 0:
                            self.logger.warning(f"无法获取BGM时长: {bgm_file}，使用默认值180秒")
                            bgm_duration = 180.0
                    except Exception as e:
                        self.logger.warning(f"获取BGM时长失败: {bgm_file}, 错误: {str(e)}")
                        bgm_duration = 180.0  # 默认3分钟
                    
                    total_bgm_duration += bgm_duration
                    self.logger.info(f"添加BGM: {os.path.basename(bgm_file)}，时长: {bgm_duration}秒，累计时长: {total_bgm_duration}秒")
                    
                    # 写入文件列表 - 处理特殊字符和中文路径问题
                    # 将Windows路径转换成正斜杠
                    safe_path = bgm_file.replace('\\', '/')
                    # 处理单引号，ffmpeg concat 格式要求
                    safe_path = safe_path.replace("'", "'\\''")
                    # 写入到列表文件
                    f.write(f"file '{safe_path}'\n")
            
            self.logger.info(f"BGM列表创建完成，总时长: {total_bgm_duration}秒，文件数: {len(selected_bgms)}")
            return list_file_path
            
        except Exception as e:
            self.logger.error(f"创建BGM列表时出错: {str(e)}")
            traceback.print_exc()
            return None


    def _extract_and_log_ffmpeg_progress(self, line):
        """从FFmpeg输出中提取进度信息并记录到日志"""
        try:
            # 提取常见的FFmpeg进度信息部分
            progress_info = {}
            
            # 获取当前时间
            current_time = time.time()
            
            # 限制日志更新频率为1秒一次 (改为1秒更新一次，而不是3秒)
            if not hasattr(self, '_last_ffmpeg_progress_update') or (current_time - self._last_ffmpeg_progress_update) >= 3.0:
                self._last_ffmpeg_progress_update = current_time
            else:
                # 未达到更新间隔，跳过日志更新
                return False
            
            # 提取帧数
            frame_match = re.search(r'frame=\s*(\d+)', line)
            if frame_match:
                progress_info['frame'] = frame_match.group(1)
            
            # 提取FPS
            fps_match = re.search(r'fps=\s*(\d+)', line)
            if fps_match:
                progress_info['fps'] = fps_match.group(1)
            
            # 提取时间
            time_match = re.search(r'time=(\d+):(\d+):(\d+\.\d+)', line)
            if time_match:
                h, m, s = time_match.groups()
                progress_info['time'] = f"{h}:{m}:{s}"
            
            # 提取比特率
            bitrate_match = re.search(r'bitrate=\s*(\d+\.\d+)', line)
            if bitrate_match:
                progress_info['bitrate'] = f"{bitrate_match.group(1)}kbits/s"
            
            # 提取大小
            size_match = re.search(r'size=\s*(\d+)(\w+)', line)
            if size_match:
                size, unit = size_match.groups()
                progress_info['size'] = f"{size}{unit}"
            
            # 提取速度
            speed_match = re.search(r'speed=\s*(\S+)', line)
            if speed_match:
                speed_val = speed_match.group(1)
                # 确保速度值显示正确的格式，可能是数字加x，也可能是其他格式
                if speed_val.endswith('x'):
                    progress_info['speed'] = speed_val
                else:
                    progress_info['speed'] = f"{speed_val}"
            
            # 提取q值
            q_match = re.search(r'q=\s*(\d+\.\d+)', line)
            if q_match:
                progress_info['q'] = q_match.group(1)
            
            # 如果至少有frame信息，则输出完整进度
            if 'frame' in progress_info:
                progress_str = "[进度] "
                progress_str += "frame=" + progress_info.get('frame', '?')
                
                if 'fps' in progress_info:
                    progress_str += f" fps={progress_info['fps']}"
                
                if 'q' in progress_info:
                    progress_str += f" q={progress_info['q']}"
                
                if 'size' in progress_info:
                    progress_str += f" size={progress_info['size']}"
                
                if 'time' in progress_info:
                    progress_str += f" time={progress_info['time']}"
                
                if 'bitrate' in progress_info:
                    progress_str += f" bitrate={progress_info['bitrate']}"
                
                if 'speed' in progress_info:
                    progress_str += f" speed={progress_info['speed']}"
                
                # 记录完整进度信息
                self.logger.info(progress_str)
                self.update_progress(None, progress_str)
                return True
            
            # 如果是其他包含关键字的FFmpeg输出
            elif "frame=" in line or "fps=" in line or "time=" in line or "size=" in line or "bitrate=" in line:
                progress_str = "[进度] " + line.strip()
                self.logger.info(progress_str)
                self.update_progress(None, progress_str)
                return True
                
            return False
        except Exception as e:
            self.logger.warning(f"解析FFmpeg进度时出错: {str(e)}")
            return False

    def process_video_with_audio(self, input_video, audio_path, output_path):
        """一步完成：创建循环视频并合并音频"""
        try:
            # 检查输入文件是否存在
            if not os.path.exists(input_video):
                self.logger.error(f"输入视频文件不存在: {input_video}")
                return False
            
            if not os.path.exists(audio_path):
                self.logger.error(f"输入音频文件不存在: {audio_path}")
                return False
            
            # 获取音频时长
            audio_info = self._get_video_info(audio_path)
            if not audio_info or 'duration' not in audio_info:
                self.logger.error(f"无法获取音频时长: {audio_path}")
                return False
            
            audio_duration = float(audio_info.get('duration', 0))
            self.logger.info(f"音频时长: {audio_duration}秒")
            
            # 检测最佳硬件编码器
            hw_encoder = self._get_optimal_encoder()
            hw_options = []
            
            # 获取自定义分辨率
            resolution_width = "1280"
            resolution_height = "720"
            if self.options.get('custom_resolution', False):
                resolution_width = self.options.get('resolution_width', '1280')
                resolution_height = self.options.get('resolution_height', '720')
                self.logger.info(f"使用自定义分辨率: {resolution_width}x{resolution_height}")
            
            # 获取自定义码率
            bitrate = "2000k"
            if self.options.get('custom_bitrate', False):
                bitrate_value = self.options.get('bitrate', '2000')
                bitrate = f"{bitrate_value}k"
                self.logger.info(f"使用自定义码率: {bitrate}")
            
            # 检查可用的硬件编码器
            self.logger.info("检测硬件编码器...")
            
            # 更新进度
            self.update_progress(25, "准备合成视频...")
            
            # 简化编码参数，使用更稳定的设置
            if self.check_nvidia_gpu():
                self.logger.info("检测到NVIDIA GPU，使用NVENC硬件加速，简化参数")
                hw_encoder = "h264_nvenc"
                hw_options = [
                    "-c:v", hw_encoder,
                    "-preset", "medium",     # 使用更稳定的medium预设
                    "-b:v", bitrate          # 使用自定义码率
                ]
            elif self.check_intel_gpu():
                self.logger.info("检测到Intel GPU，使用QSV硬件加速")
                hw_encoder = "h264_qsv"
                hw_options = [
                    "-c:v", hw_encoder,
                    "-preset", "medium",
                    "-b:v", bitrate          # 使用自定义码率
                ]
            elif self.check_amd_gpu():
                self.logger.info("检测到AMD GPU，使用AMF硬件加速")
                hw_encoder = "h264_amf"
                hw_options = [
                    "-c:v", hw_encoder,
                    "-quality", "speed",        # 速度优先模式
                    "-b:v", bitrate,            # 使用自定义比特率
                    "-usage", "transcoding"     # 使用转码模式而非超低延迟
                ]
            else:
                self.logger.info("未检测到支持的GPU，使用软件编码")
                hw_encoder = "libx264"
                hw_options = [
                    "-c:v", hw_encoder,
                    "-preset", "medium",      # 稳定的中速预设
                    "-crf", "30",             # 高质量CRF值
                    "-b:v", bitrate           # 使用自定义码率
                ]
            
            # 构建命令：直接在一步中完成视频循环和音频合并
            cmd = [
                "ffmpeg", "-y",
                "-stream_loop", "-1",         # 无限循环输入视频
                "-i", f'"{input_video}"',     # 输入视频
                "-i", f'"{audio_path}"',      # 输入音频
                "-t", str(audio_duration),    # 使用音频时长作为输出视频时长
                "-vf", f"scale={resolution_width}:{resolution_height}",  # 使用自定义分辨率
                "-c:a", "aac",                # 音频编码
                "-b:a", "98k",               # 音频比特率
                "-map", "0:v",                # 使用第一个输入的视频
                "-map", "1:a",                # 使用第二个输入的音频
                "-shortest",                  # 使用最短的输入长度
            ]
            
            # 添加编码器特定选项
            cmd.extend(hw_options)
            
            # 添加输出路径
            cmd.append(f'"{output_path}"')
            
            # 构建完整命令字符串
            cmd_str = " ".join(cmd)
            
            # 在Windows下使用cmd执行
            if platform.system() == 'Windows':
                cmd_str = f'cmd /c {cmd_str}'
            
            self.logger.info(f"执行一步合成命令: {cmd_str}")
            self.update_progress(30, "合成循环视频...")
            
            # 执行命令
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE, 
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                 process = subprocess.Popen(
                    cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE, 
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 添加超时处理和进度显示
            start_time = time.time()
            last_update_time = start_time
            
            # 读取并处理输出
            while process.poll() is None:
                # 读取stderr获取进度信息
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        # 使用专门的方法提取并记录FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)
            
            # 获取结果
            result = process.wait()
            
            # 检查结果
            if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"一步合成视频成功: {output_path}")
                return True
            else:
                self.logger.error(f"一步合成视频失败，返回码: {result}")
                stderr_output = ""
                if process.stderr:
                    try:
                        stderr_output = process.stderr.read()
                    except:
                        pass
                
        except Exception as e:
            self.logger.error(f"一步合成视频时出错: {str(e)}")
            traceback.print_exc()
            return False

    def _get_free_disk_space(self, path):
        """获取指定路径所在磁盘的可用空间（字节）"""
        try:
            if os.name == 'nt':  # Windows
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(path), None, None, ctypes.pointer(free_bytes))
                return free_bytes.value
            else:  # Unix/Linux/MacOS
                st = os.statvfs(path)
                return st.f_bavail * st.f_frsize
        except:
            return 0  # 如果无法获取，返回0
    
    def _format_size(self, size_bytes):
        """将字节大小格式化为人类可读形式"""
        if size_bytes == 0:
            return "0B"
        size_names = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def _preprocess_video_for_merge(self, video, index, temp_dir):
        """预处理单个视频文件，用于并行处理"""
        try:
            result = {
                'success': False, 
                'output_path': video,  # 默认使用原视频
                'temp_audio': None
            }
            
            # 提取视频信息
            video_info = self._get_video_info(video)
            if not video_info:
                self.logger.warning(f"无法获取视频 {index+1} 信息，使用原视频")
                return result
            
            # 检查音频编码
            has_audio_issue = False
            if video_info and 'audio_codec' in video_info:
                # 如果是AAC或未知编码，添加到可能有问题的列表
                if video_info.get('audio_codec') in ['aac', None, 'unknown']:
                    has_audio_issue = True
            else:
                # 无法获取编码信息，假设可能有问题
                has_audio_issue = True
            
            # 如果没有音频问题，直接返回原视频
            if not has_audio_issue:
                result['success'] = True
                return result
            
            # 修复有问题的音频
            output_fixed = os.path.join(temp_dir, f"fixed_{index+1}_{os.path.basename(video)}")
            temp_audio = os.path.join(temp_dir, f"temp_audio_{index+1}.aac")
            
            # 提取并转码音频流到临时文件
            audio_cmd = (
                f'ffmpeg -y -i "{video}" -vn -c:a aac -b:a 128k -ar 44100 '
                f'-ac 2 -map 0:a:0 -strict experimental "{temp_audio}"'
            )
            
            if platform.system() == 'Windows':
                audio_cmd = f'cmd /c {audio_cmd}'
            
            subprocess.run(audio_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            
            # 检查音频文件是否生成成功
            if os.path.exists(temp_audio) and os.path.getsize(temp_audio) > 0:
                # 将视频流和修复后的音频流合并
                fix_cmd = (
                    f'ffmpeg -y -i "{video}" -i "{temp_audio}" '
                    f'-c:v copy -c:a copy -map 0:v:0 -map 1:a:0 -pix_fmt yuv420p '
                    f'-shortest "{output_fixed}"'
                )
                
                if platform.system() == 'Windows':
                    fix_cmd = f'cmd /c {fix_cmd}'
                
                subprocess.run(fix_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                
                # 检查修复后的视频
                if os.path.exists(output_fixed) and os.path.getsize(output_fixed) > 0:
                    result['success'] = True
                    result['output_path'] = output_fixed
                    result['temp_audio'] = temp_audio
                    return result
            
            # 如果处理失败，使用原视频
            return result
            
        except Exception as e:
            self.logger.error(f"预处理视频 {index+1} 时出错: {str(e)}")
            return {
                'success': False, 
                'output_path': video,
                'temp_audio': None
            }
        
   
    def _simple_merge_audio_video(self, audio_path, video_path, output_path):
        """使用最简单的方式合并音频和视频 - 特别优化Windows下的中文路径处理"""
        try:
            # 构建简单命令，使用双引号包围路径
            cmd_parts = [
                "ffmpeg", "-y",
                "-i", f'"{video_path}"',
                "-i", f'"{audio_path}"',
                "-c:v", "copy",       # 直接复制视频流
                "-c:a", "aac",        # 重新编码音频为AAC
                "-b:a", "192k",       # 音频比特率
                "-map", "0:v",        # 使用第一个输入的视频
                "-map", "1:a",        # 使用第二个输入的音频
                "-shortest",          # 使用最短的输入长度
                f'"{output_path}"'    # 输出路径用引号包围
            ]
            
            # 构建命令字符串
            cmd_str = " ".join(cmd_parts)
            
            # 在Windows下使用cmd执行
            if platform.system() == 'Windows':
                cmd_str = f'cmd /c {cmd_str}'
            
            self.logger.info(f"执行简化合并命令: {cmd_str}")
            
            # 执行命令
            if platform.system() == 'Windows':
                process = subprocess.Popen(
                    cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace',
                    startupinfo=self.startupinfo,
                    creationflags=self.creation_flags
                )
            else:
                process = subprocess.Popen(
                    cmd_str,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    encoding='utf-8',
                    errors='replace'
                )
            
            # 添加超时处理和进度显示
            start_time = time.time()
            last_update_time = start_time
            
            # 读取日志和进度，不设置超时
            while process.poll() is None:
                # 读取stderr获取进度信息
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        # 使用专门的方法提取并记录FFmpeg进度
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:
                        time.sleep(0.1)
                else:
                    time.sleep(0.1)
            
            # 获取结果
            result = process.wait()
            
            # 检查结果
            if result == 0 and os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                self.logger.info(f"简化合并成功: {output_path}")
                return True
            else:
                self.logger.error(f"简化合并失败，返回码: {result}")
                stderr_output = ""
                if process.stderr:
                    try:
                        stderr_output = process.stderr.read()
                    except:
                        pass
                
                if stderr_output:
                    self.logger.error(f"错误信息: {stderr_output}")
                return False
        
        except Exception as e:
            self.logger.error(f"简化合并时出错: {str(e)}")
            return False


class SubtitleSettingsDialog:
    def __init__(self, parent, subtitle_style=None, callback=None):
        """初始化字幕设置对话框"""
        self.parent = parent
        self.callback = callback
        self.subtitle_style = subtitle_style or {}
        
        # 创建对话框窗口
        self.window = tk.Toplevel(parent)
        self.window.title("字幕设置")
        
        # 使用TkS函数适配DPI
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()  # 模态对话框
        
        # 主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="字幕样式设置", padding=10)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 字体设置
        font_frame = ttk.Frame(settings_frame)
        font_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(font_frame, text="字体:").pack(side=tk.LEFT, padx=5)
        self.font_var = tk.StringVar(value=self.subtitle_style.get("font", "微软雅黑"))
        fonts = ["微软雅黑", "宋体", "黑体", "楷体", "Arial", "Times New Roman"]
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_var, values=fonts, width=15)
        font_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(font_frame, text="大小:").pack(side=tk.LEFT, padx=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_style.get("font_size", 22))
        ttk.Spinbox(font_frame, from_=8, to=72, textvariable=self.font_size_var, width=5).pack(side=tk.LEFT, padx=5)
        
        # 字体样式
        style_frame = ttk.Frame(settings_frame)
        style_frame.pack(fill=tk.X, pady=5)
        
        self.bold_var = tk.BooleanVar(value=self.subtitle_style.get("bold", True))
        ttk.Checkbutton(style_frame, text="粗体", variable=self.bold_var).pack(side=tk.LEFT, padx=5)
        
        self.italic_var = tk.BooleanVar(value=self.subtitle_style.get("italic", False))
        ttk.Checkbutton(style_frame, text="斜体", variable=self.italic_var).pack(side=tk.LEFT, padx=5)
        
        self.outline_var = tk.BooleanVar(value=self.subtitle_style.get("outline", True))
        ttk.Checkbutton(style_frame, text="描边", variable=self.outline_var).pack(side=tk.LEFT, padx=5)
        
        # 颜色设置
        color_frame = ttk.Frame(settings_frame)
        color_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(color_frame, text="字体颜色:").pack(side=tk.LEFT, padx=5)
        self.font_color_var = tk.StringVar(value=self.subtitle_style.get("font_color", "白色"))
        colors = ["白色", "黑色", "黄色", "橙色","红色", "绿色", "蓝色", "青色", "洋红色"]
        ttk.Combobox(color_frame, textvariable=self.font_color_var, values=colors, width=10).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(color_frame, text="描边颜色:").pack(side=tk.LEFT, padx=5)
        self.outline_color_var = tk.StringVar(value=self.subtitle_style.get("outline_color", "黑色"))
        ttk.Combobox(color_frame, textvariable=self.outline_color_var, values=colors, width=10).pack(side=tk.LEFT, padx=5)
        
        # 描边粗细
        outline_width_frame = ttk.Frame(settings_frame)
        outline_width_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(outline_width_frame, text="描边粗细:").pack(side=tk.LEFT, padx=5)
        # 确保有默认值，防止空字符串导致的错误
        default_outline_width = self.subtitle_style.get("outline_width", 2.0)
        # 兼容旧版本的depth参数
        if "depth" in self.subtitle_style:
            default_outline_width = self.subtitle_style.get("depth", 2.0)
        
        self.outline_width_var = tk.DoubleVar(value=default_outline_width)
        outline_width_spinner = ttk.Spinbox(
            outline_width_frame, 
            from_=0.1, 
            to=5.0, 
            increment=0.1, 
            textvariable=self.outline_width_var, 
            width=5
        )
        outline_width_spinner.pack(side=tk.LEFT, padx=5)
        
        # 位置设置
        position_frame = ttk.Frame(settings_frame)
        position_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(position_frame, text="位置:").pack(side=tk.LEFT, padx=5)
        self.position_var = tk.StringVar(value=self.subtitle_style.get("position", "底部"))
        # 新增"中间"选项
        positions = ["底部", "顶部", "中间", "左上角", "右上角", "左下角", "右下角"]
        ttk.Combobox(position_frame, textvariable=self.position_var, values=positions, width=10).pack(side=tk.LEFT, padx=5)
        
        # 预览框架
        preview_frame = ttk.LabelFrame(main_frame, text="预览效果", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, width=780, height=300, bg="white")
        self.preview_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定变更事件
        self.font_var.trace_add("write", self.update_preview)
        self.font_size_var.trace_add("write", self.update_preview)
        self.bold_var.trace_add("write", self.update_preview)
        self.italic_var.trace_add("write", self.update_preview)
        self.outline_var.trace_add("write", self.update_preview)
        self.font_color_var.trace_add("write", self.update_preview)
        self.outline_color_var.trace_add("write", self.update_preview)
        self.outline_width_var.trace_add("write", self.update_preview)
        self.position_var.trace_add("write", self.update_preview)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 使用ttk.Button而不是tk.Button，确保按钮可点击
        ttk.Button(button_frame, text="确定", command=self.confirm).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 设置窗口居中
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
        # 在窗口显示后延迟100毫秒更新预览，确保画布已完全加载
        self.window.after(100, self.update_preview)
        
    def update_preview(self, *args):
        """更新预览效果"""
        try:
            # 清除画布
            self.preview_canvas.delete("all")
            
            # 获取字体样式
            font_style = ""
            if self.bold_var.get():
                font_style += " bold"
            if self.italic_var.get():
                font_style += " italic"  # 修复斜体样式格式
            
            # 创建字体
            font_name = self.font_var.get()
            try:
                font_size = int(self.font_size_var.get())
            except (ValueError, tk.TclError):
                font_size = 22  # 默认字体大小
            font = (font_name, font_size, font_style.strip())
            
            # 示例文本
            text = "字幕示例 Sample Text 123"
            
            # 字体颜色 - 直接使用中文颜色名
            font_color = self.convert_color_for_display(self.font_color_var.get())
            
            # 计算文本宽度和高度
            text_id = self.preview_canvas.create_text(0, 0, text=text, font=font, fill=font_color)
            bbox = self.preview_canvas.bbox(text_id)
            self.preview_canvas.delete(text_id)
            
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 根据位置放置文本
            canvas_width = self.preview_canvas.winfo_width() or 780
            canvas_height = self.preview_canvas.winfo_height() or 300
            
            position = self.position_var.get()
            if position == "底部":
                x = canvas_width // 2
                y = canvas_height - 20
                anchor = tk.S
            elif position == "顶部":
                x = canvas_width // 2
                y = 20
                anchor = tk.N
            elif position == "中间":
                x = canvas_width // 2
                y = canvas_height // 2
                anchor = tk.CENTER
            elif position == "左上角":
                x = 20
                y = 20
                anchor = tk.NW
            elif position == "右上角":
                x = canvas_width - 20
                y = 20
                anchor = tk.NE
            elif position == "左下角":
                x = 20
                y = canvas_height - 20
                anchor = tk.SW
            elif position == "右下角":
                x = canvas_width - 20
                y = canvas_height - 20
                anchor = tk.SE
            else:
                x = canvas_width // 2
                y = canvas_height - 20
                anchor = tk.S
            
            # 绘制描边
            if self.outline_var.get():
                # 获取描边粗细
                outline_width = 2.0  # 默认值
                try:
                    # 获取outline_width_var的值，如果为空字符串则使用默认值
                    width_str = self.outline_width_var.get()
                    # 确保不是空字符串，且可以转换为浮点数
                    if width_str and isinstance(width_str, (int, float)):
                        outline_width = float(width_str)
                    elif isinstance(width_str, str) and width_str.strip():
                        outline_width = float(width_str.strip())
                except (ValueError, tk.TclError) as e:
                    print(f"描边宽度转换错误: {e}，使用默认值2.0")
                
                # 处理描边颜色 - 直接使用中文颜色名
                outline_color = self.convert_color_for_display(self.outline_color_var.get())
                
                # 绘制描边效果（简易模拟）
                for dx, dy in [(-1, -1), (-1, 1), (1, -1), (1, 1), (-1, 0), (1, 0), (0, -1), (0, 1)]:
                    self.preview_canvas.create_text(
                        x + dx * outline_width, 
                        y + dy * outline_width, 
                        text=text, 
                        font=font, 
                        fill=outline_color,
                        anchor=anchor
                    )
            
            # 绘制主体文本
            self.preview_canvas.create_text(
                x, y, text=text, font=font, fill=font_color, anchor=anchor
            )
        except Exception as e:
            print(f"预览更新错误: {e}")
    
    def convert_color_for_display(self, color_name):
        """颜色转换函数，仅用于Canvas预览显示（预览时需要使用英文颜色）"""
        color_map = {
            "白色": "white",
            "黑色": "black",
            "黄色": "yellow",
            "橙色": "orange",
            "红色": "red",
            "绿色": "green",
            "蓝色": "blue",
            "青色": "cyan",
            "洋红色": "magenta"
        }
        # 仅预览时需要英文颜色，实际处理时保留中文颜色名
        return color_map.get(color_name, color_name)
    
    def confirm(self):
        """确认设置"""
        # 获取并处理可能出现的TclError
        try:
            width_str = self.outline_width_var.get()
            if isinstance(width_str, str) and not width_str.strip():
                outline_width = 2.0  # 默认值
            else:
                outline_width = float(width_str)
        except (ValueError, tk.TclError):
            outline_width = 2.0  # 使用默认值
            
        # 获取并处理字体大小
        try:
            font_size = int(self.font_size_var.get())
        except (ValueError, tk.TclError):
            font_size = 22  # 默认字体大小
            
        # 收集字幕样式 - 直接使用中文颜色名
        subtitle_style = {
            "font": self.font_var.get(),
            "font_size": font_size,
            "bold": self.bold_var.get(),
            "italic": self.italic_var.get(),
            "outline": self.outline_var.get(),
            "outline_width": outline_width,  # 使用outline_width，而不是depth
            "outline_color": self.outline_color_var.get(),  # 保留原始中文颜色名
            "font_color": self.font_color_var.get(),  # 保留原始中文颜色名
            "position": self.position_var.get()
        }
        
        if self.callback:
            self.callback(subtitle_style)
        
        self.window.destroy()


class VideoProcessingApp:
    # 静态变量，用于防止重复显示弹窗
    _completion_dialog_shown = False
    
    def __init__(self, root):
        self.root = root
        self.root.title("视频自动合成工具")


        
        


        # 初始化变量
        self.process_folder_path = ""
        self.valid_subfolders = []  # 存储有效的子文件夹
        self.bgm_path = ""
        self.add_bgm_var = tk.BooleanVar(value=False)
        self.bgm_mode_var = tk.IntVar(value=1)  # 1: 单文件, 2: 文件夹随机
        self.bgm_volume_var = tk.DoubleVar(value=0.2)  # 添加BGM音量控制变量，默认20%
        self.add_subtitle_var = tk.BooleanVar(value=False)
        self.subtitle_style_var = tk.StringVar(value="默认样式")
        self.create_merged_video_var = tk.BooleanVar(value=False)  # 新增：是否创建合并视频
        self.merge_original_video_var = tk.BooleanVar(value=False)  # 新增：是否合并原视频与第一集
        
        # 添加并行处理设置
        self.parallel_processing_var = tk.BooleanVar(value=True)
        self.max_workers_var = tk.IntVar(value=min(4, os.cpu_count() or 4))  # 默认为4或CPU核心数
        
        # 添加自定义分辨率和码率设置
        self.custom_resolution_var = tk.BooleanVar(value=False)  # 是否使用自定义分辨率
        self.resolution_width_var = tk.StringVar(value="1280")  # 默认宽度
        self.resolution_height_var = tk.StringVar(value="720")  # 默认高度
        self.custom_bitrate_var = tk.BooleanVar(value=False)  # 是否使用自定义码率
        self.bitrate_var = tk.StringVar(value="2000")  # 默认码率，单位为k
        
        # 进度条平滑设置
        self.last_progress_value = 0
        self.target_progress_value = 0
        self.smooth_animation_active = False
        
        # ------------- 预设相关变量 -------------
        self.presets = {}  # 用于存储读取到的全部预设

        # 读取磁盘中的预设文件
        self.load_presets()

        # 字幕设置对话框的结果
        self.subtitle_settings = None
        
        # 创建处理线程
        self.processing_thread = None
        
        # 创建主布局
        self.create_ui()
        
        # 绑定窗口关闭事件，确保退出时清理后台FFmpeg进程
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)  # type: ignore[arg-type]
    
    def update_status(self, text, info=""):
        """更新状态栏信息"""
        # 将文本直接设置到统一状态栏
        self.status_display.config(text=text)
        self.root.update_idletasks()
    
    def create_ui(self):
        # 创建主框架 - 左右分栏
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # ---------------- 带滚动条的左侧控制面板 ----------------
        left_container = ttk.Frame(main_frame)
        left_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 5))

        # 使用 Canvas + Scrollbar 组合实现滚动
        # 创建Canvas作为滚动区域的容器，highlightthickness设置为25
        left_canvas = tk.Canvas(left_container, highlightthickness=0)  # 固定宽度450像素
        # 创建垂直滚动条并绑定到Canvas的yview
        vsb = ttk.Scrollbar(left_container, orient="vertical", command=left_canvas.yview)
        # 将滚动条放置在右侧
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        # 将Canvas放置在左侧，并允许它填充和扩展
        left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        # 设置Canvas的滚动命令与滚动条关联
        left_canvas.configure(yscrollcommand=vsb.set)

        # 真正承载控件的 Frame
        left_frame = ttk.LabelFrame(left_canvas, text="控制面板", padding="10")
        left_canvas.create_window((0, 0), window=left_frame, anchor="nw")

        # 动态更新 scrollregion
        def _cfg(event):
            left_canvas.configure(scrollregion=left_canvas.bbox("all"))
        left_frame.bind("<Configure>", _cfg)

        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            left_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        # 仅在鼠标位于左侧滚动区域时才滚动，避免影响右侧日志区域
        left_canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # 右侧信息显示面板
        right_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 创建底部状态显示区域
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态栏（底部）- 简化为单一状态条
        self.status_bar = ttk.Frame(status_frame, relief=tk.SUNKEN, borderwidth=1)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        # 统一显示区域
        self.status_display = ttk.Label(self.status_bar, text="就绪", anchor=tk.W)
        self.status_display.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        
        # 定时器 ID，用于取消定时器
        self.timer_id = None
        
        # ------ 左侧控制面板内容 ------

        # --------------------------------------
        # 参数预设（移动到最顶部）
        # --------------------------------------
        preset_frame = ttk.LabelFrame(left_frame, text="参数预设", padding="10")
        preset_frame.grid(row=0, column=0, columnspan=3, sticky=tk.EW, pady=(0, 10))

        # 下拉选择框
        self.preset_combo = ttk.Combobox(preset_frame, state="readonly", width=18)
        self.preset_combo.grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.preset_combo.bind("<<ComboboxSelected>>", self.apply_selected_preset)
        
        # 保存 / 删除 按钮
        ttk.Button(preset_frame, text="保存为预设", command=self.save_current_as_preset).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(preset_frame, text="删除预设", command=self.delete_selected_preset).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 预填充下拉框
        self.refresh_preset_combo()

        # 处理文件夹选择
        ttk.Label(left_frame, text="处理文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.process_folder_label = ttk.Label(left_frame, text="未选择文件夹")
        self.process_folder_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Button(left_frame, text="浏览", command=self.select_process_folder).grid(row=1, column=2, sticky=tk.W, pady=5)
        
        # 子文件夹信息显示
        ttk.Label(left_frame, text="有效子文件夹:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.subfolder_count_label = ttk.Label(left_frame, text="未检测")
        self.subfolder_count_label.grid(row=2, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # BGM设置
        ttk.Separator(left_frame, orient='horizontal').grid(row=3, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Checkbutton(left_frame, text="添加BGM", variable=self.add_bgm_var).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # BGM路径
        ttk.Label(left_frame, text="BGM路径:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.bgm_path_label = ttk.Label(left_frame, text="未选择BGM")
        self.bgm_path_label.grid(row=5, column=1, sticky=tk.W, pady=5)
        ttk.Button(left_frame, text="浏览", command=self.select_bgm_path).grid(row=5, column=2, sticky=tk.W, pady=5)
        
        # BGM模式
        ttk.Label(left_frame, text="BGM模式:").grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Radiobutton(left_frame, text="单文件循环", variable=self.bgm_mode_var, value=1).grid(row=6, column=1, sticky=tk.W, pady=5)
        ttk.Radiobutton(left_frame, text="文件夹随机", variable=self.bgm_mode_var, value=2).grid(row=7, column=1, sticky=tk.W, pady=5)
        
        # 添加BGM音量控制
        ttk.Label(left_frame, text="BGM音量:").grid(row=8, column=0, sticky=tk.W, pady=5)
        volume_frame = ttk.Frame(left_frame)
        volume_frame.grid(row=8, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        self.bgm_volume_scale = ttk.Scale(
            volume_frame, 
            from_=0.0, 
            to=1.0, 
            orient="horizontal", 
            length=150, 
            variable=self.bgm_volume_var,
            command=self.update_volume_label
        )
        self.bgm_volume_scale.pack(side=tk.LEFT, padx=(0, 5))
        
        self.bgm_volume_label = ttk.Label(volume_frame, text="20%")
        self.bgm_volume_label.pack(side=tk.LEFT)
        
        # 字幕设置
        ttk.Separator(left_frame, orient='horizontal').grid(row=9, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Checkbutton(left_frame, text="添加字幕", variable=self.add_subtitle_var).grid(row=10, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # 字幕样式设置按钮
        subtitle_settings_frame = ttk.Frame(left_frame)
        subtitle_settings_frame.grid(row=11, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        self.subtitle_style_label = ttk.Label(subtitle_settings_frame, text="字幕样式: 未设置")
        self.subtitle_style_label.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(subtitle_settings_frame, text="设置样式", command=self.open_subtitle_settings).pack(side=tk.LEFT)
        
        # 输出选项
        ttk.Separator(left_frame, orient='horizontal').grid(row=12, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text="输出选项:").grid(row=13, column=0, sticky=tk.W, pady=5)
        
        # 合并视频选项（默认不勾选）
        merged_video_check = ttk.Checkbutton(
            left_frame, 
            text="创建合并视频（合并为单视频）", 
            variable=self.create_merged_video_var

        )
        merged_video_check.grid(row=13, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 合并原视频选项（默认不勾选）
        merge_original_check = ttk.Checkbutton(
            left_frame, 
            text="合并原视频与第一集", 
            variable=self.merge_original_video_var
        )
        merge_original_check.grid(row=14, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 操作按钮
        ttk.Separator(left_frame, orient='horizontal').grid(row=15, column=0, columnspan=3, sticky=tk.EW, pady=10)
        self.start_button = ttk.Button(left_frame, text="开始处理", command=self.start_processing)
        self.start_button.grid(row=16, column=0, columnspan=2, sticky=tk.W, pady=10)
        
        self.cancel_button = ttk.Button(left_frame, text="取消", command=self.cancel_processing, state=tk.DISABLED)
        self.cancel_button.grid(row=16, column=2, sticky=tk.W, pady=10)
        
        # 添加并行处理选项到左侧面板
        ttk.Separator(left_frame, orient='horizontal').grid(row=25, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text="性能选项:").grid(row=26, column=0, sticky=tk.W, pady=5)
        
        parallel_check = ttk.Checkbutton(
            left_frame, 
            text="启用并行处理", 
            variable=self.parallel_processing_var
        )
        parallel_check.grid(row=26, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 线程数设置
        ttk.Label(left_frame, text="最大线程数:").grid(row=27, column=0, sticky=tk.W, pady=5)
        worker_spinner = ttk.Spinbox(
            left_frame, 
            from_=1, 
            to=max(8, os.cpu_count() or 4), 
            textvariable=self.max_workers_var, 
            width=5
        )
        worker_spinner.grid(row=27, column=1, sticky=tk.W, pady=5)
        
        # 添加提示标签
        ttk.Label(left_frame, text="(无需修改)").grid(row=27, column=2, sticky=tk.W, pady=5)
        
        # 添加视频设置选项
        ttk.Separator(left_frame, orient='horizontal').grid(row=28, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text="视频设置:").grid(row=29, column=0, sticky=tk.W, pady=5)
        
        # 分辨率设置
        custom_resolution_check = ttk.Checkbutton(
            left_frame,
            text="自定义分辨率",
            variable=self.custom_resolution_var
        )
        custom_resolution_check.grid(row=29, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 分辨率输入框
        resolution_frame = ttk.Frame(left_frame)
        resolution_frame.grid(row=30, column=0, columnspan=3, sticky=tk.W, pady=5, padx=(20, 0))
        
        ttk.Label(resolution_frame, text="宽:").pack(side=tk.LEFT, padx=(0, 5))
        width_entry = ttk.Entry(resolution_frame, textvariable=self.resolution_width_var, width=6)
        width_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(resolution_frame, text="高:").pack(side=tk.LEFT, padx=(0, 5))
        height_entry = ttk.Entry(resolution_frame, textvariable=self.resolution_height_var, width=6)
        height_entry.pack(side=tk.LEFT)
        
        # 码率设置
        custom_bitrate_check = ttk.Checkbutton(
            left_frame,
            text="自定义码率",
            variable=self.custom_bitrate_var
        )
        custom_bitrate_check.grid(row=31, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # 码率输入框
        bitrate_frame = ttk.Frame(left_frame)
        bitrate_frame.grid(row=32, column=0, columnspan=3, sticky=tk.W, pady=5, padx=(20, 0))
        
        ttk.Label(bitrate_frame, text="码率(kbps):").pack(side=tk.LEFT, padx=(0, 5))
        bitrate_entry = ttk.Entry(bitrate_frame, textvariable=self.bitrate_var, width=8)
        bitrate_entry.pack(side=tk.LEFT)
        
        # --------------------------------------
        # 参数预设（顺延到性能选项之后）
        # --------------------------------------
        ttk.Separator(left_frame, orient='horizontal').grid(row=35, column=0, columnspan=3, sticky=tk.EW, pady=10)
        
        # ------ 右侧信息显示面板内容 ------
        # 当前操作标签
        self.current_operation_label = ttk.Label(right_frame, text="当前操作: 就绪")
        self.current_operation_label.pack(anchor=tk.W, pady=10)
        
        # 处理参数显示
        parameter_frame = ttk.LabelFrame(right_frame, text="处理参数")
        parameter_frame.pack(fill=tk.X, pady=10)
        
        self.param_folder_label = ttk.Label(parameter_frame, text="处理文件夹: 未选择")
        self.param_folder_label.pack(anchor=tk.W, pady=2)
        
        self.param_subfolder_label = ttk.Label(parameter_frame, text="有效子文件夹: 0")
        self.param_subfolder_label.pack(anchor=tk.W, pady=2)
        
        self.param_bgm_label = ttk.Label(parameter_frame, text="BGM: 不添加")
        self.param_bgm_label.pack(anchor=tk.W, pady=2)
        
        self.param_subtitle_label = ttk.Label(parameter_frame, text="字幕: 不添加")
        self.param_subtitle_label.pack(anchor=tk.W, pady=2)
        
        self.param_merged_label = ttk.Label(parameter_frame, text="合并视频: 不创建")
        self.param_merged_label.pack(anchor=tk.W, pady=2)
        
        self.param_resolution_label = ttk.Label(parameter_frame, text="分辨率: 默认(1280x720)")
        self.param_resolution_label.pack(anchor=tk.W, pady=2)
        
        self.param_bitrate_label = ttk.Label(parameter_frame, text="码率: 默认(2000k)")
        self.param_bitrate_label.pack(anchor=tk.W, pady=2)
        
        # 日志显示
        ttk.Label(right_frame, text="处理日志:").pack(anchor=tk.W, pady=5)
        log_frame = ttk.Frame(right_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志文本框，使用等宽字体以便于阅读FFmpeg输出
        # 使用固定字体大小
        font_size = 9
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=25, font=("Consolas", font_size))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # 添加日志右键菜单
        self.log_context_menu = tk.Menu(self.log_text, tearoff=0)
        self.log_context_menu.add_command(label="复制", command=self.copy_log_selection)
        self.log_context_menu.add_command(label="复制全部", command=self.copy_all_logs)
        self.log_context_menu.add_separator()
        self.log_context_menu.add_command(label="清空日志", command=self.clear_logs)
        
        # 绑定右键菜单
        self.log_text.bind("<Button-3>", self.show_log_context_menu)
        
        # 初始化日志并禁用编辑
        self.log_text.insert(tk.END, f"程序已启动，请选择处理文件夹并点击'开始处理'按钮。\n")

        self.log_text.config(state=tk.DISABLED)
    
    def select_bgm_path(self):
        if self.bgm_mode_var.get() == 1:  # 单文件模式
            file_path = filedialog.askopenfilename(
                title="选择BGM文件",
                filetypes=[("音频文件", "*.mp3 *.wav *.flac")]
            )
            if file_path:
                self.bgm_path = file_path
                self.bgm_path_label.config(text=os.path.basename(file_path))
                
                # 更新显示时包含音量信息
                volume_percent = int(self.bgm_volume_var.get() * 100)
                self.param_bgm_label.config(text=f"BGM: {os.path.basename(file_path)} (音量: {volume_percent}%)")
                self.log_message(f"已选择BGM文件: {os.path.basename(file_path)}")
        else:  # 文件夹随机模式
            folder_path = filedialog.askdirectory(title="选择BGM文件夹")
            if folder_path:
                self.bgm_path = folder_path
                self.bgm_path_label.config(text=os.path.basename(folder_path))
                
                # 更新显示时包含音量信息
                volume_percent = int(self.bgm_volume_var.get() * 100)
                self.param_bgm_label.config(text=f"BGM: {os.path.basename(folder_path)}文件夹 (音量: {volume_percent}%)")
                self.log_message(f"已选择BGM文件夹: {os.path.basename(folder_path)}")
    
    def select_process_folder(self):
        folder_path = filedialog.askdirectory(title="选择处理文件夹")
        if folder_path:
            self.process_folder_path = folder_path
            self.process_folder_label.config(text=os.path.basename(folder_path))
            self.param_folder_label.config(text=f"处理文件夹: {os.path.basename(folder_path)}")
            self.log_message(f"已选择处理文件夹: {os.path.basename(folder_path)}")
            
            # 检测有效子文件夹和原始视频
            self.scan_folder(folder_path)
    
    def scan_folder(self, folder_path):
        """扫描文件夹，检测有效子文件夹（必须包含非空的视频和音频文件夹）"""
        self.valid_subfolders = []
        
        try:
            # 列出主文件夹中的所有子文件夹
            if not folder_path or not os.path.exists(folder_path):
                self.log_message(f"错误: 文件夹路径无效或不存在: {folder_path}")
                return
                
            folder_contents = os.listdir(folder_path)
            potential_subfolders = [f for f in folder_contents 
                               if os.path.isdir(os.path.join(folder_path, f))]
            
            # 检查每个子文件夹是否包含"视频"和"音频"
            for subfolder in potential_subfolders:
                if not subfolder:
                    continue
                    
                subfolder_path = os.path.join(folder_path, subfolder)
                if not os.path.exists(subfolder_path):
                    continue
                    
                subfolder_contents = os.listdir(subfolder_path)
                
                has_video_folder = False
                has_audio_folder = False
                
                # 检查视频和音频文件夹（并确保它们不为空）
                if "视频" in subfolder_contents and "音频" in subfolder_contents:
                    video_path = os.path.join(subfolder_path, "视频")
                    audio_path = os.path.join(subfolder_path, "音频")
                    
                    # 检查是否是目录并且不为空
                    if os.path.isdir(video_path):
                        video_files = [f for f in os.listdir(video_path) 
                                      if os.path.isfile(os.path.join(video_path, f)) and 
                                      f.lower().endswith(('.mp4', '.avi', '.mkv', '.mov'))]
                        has_video_folder = len(video_files) > 0
                    
                    if os.path.isdir(audio_path):
                        audio_files = [f for f in os.listdir(audio_path) 
                                      if os.path.isfile(os.path.join(audio_path, f)) and 
                                      f.lower().endswith(('.mp3', '.wav', '.aac', '.flac', '.m4a'))]
                        has_audio_folder = len(audio_files) > 0
                
                # 只需同时满足视频和音频两个条件
                if has_video_folder and has_audio_folder:
                    self.valid_subfolders.append(subfolder_path)  # 存储完整路径而不是仅文件夹名
            
            # 按名称排序
            self.valid_subfolders.sort()
            
            # 更新UI显示
            subfolder_count = len(self.valid_subfolders)
            self.subfolder_count_label.config(text=f"找到 {subfolder_count} 个")
            self.param_subfolder_label.config(text=f"有效子文件夹: {subfolder_count}")
            
            if subfolder_count > 0:
                subfolder_display_names = []
                for sf in self.valid_subfolders[:3]:
                    if sf:  # 确保路径不为None
                        subfolder_display_names.append(os.path.basename(sf))
                subfolder_names = ", ".join(subfolder_display_names)
                if subfolder_count > 3:
                    subfolder_names += f"... (共{subfolder_count}个)"
                self.log_message(f"检测到 {subfolder_count} 个有效子文件夹: {subfolder_names}")
            else:
                self.log_message("警告: 未检测到有效的子文件夹。每个有效子文件夹必须包含非空的'视频'文件夹和非空的'音频'文件夹。")
                messagebox.showwarning("警告", "未检测到有效的子文件夹。\n请确保每个子文件夹内包含非空的'视频'文件夹和非空的'音频'文件夹。")
        
        except Exception as e:
            self.log_message(f"扫描文件夹时出错: {str(e)}")
            messagebox.showerror("错误", f"扫描文件夹时出错: {str(e)}")
            self.valid_subfolders = []
            self.subfolder_count_label.config(text="扫描失败")
            self.param_subfolder_label.config(text="有效子文件夹: 0")
    
    def update_progress(self, value, status_info=""):
        """在日志中显示进度信息，不再使用进度条"""
        # 更新状态信息
        operation = ""
        progress_percent = ""
        remaining_info = ""
        time_info = ""
        
        # 计算已用时间
        if hasattr(self, 'start_time'):
            elapsed = time.time() - self.start_time
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_info = f"耗时: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        if status_info:
            # 获取当前时间戳
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            current_time = time.time()
            
            # 处理FFmpeg标准输出格式
            if "frame=" in status_info:
                # 这是一个FFmpeg进度信息，限制为3秒更新一次
                if not hasattr(self, '_last_log_update_time') or (current_time - self._last_log_update_time) >= 3.0:
                    self._last_log_update_time = current_time
                    self.log_message(status_info, "PROGRESS")
                
                # 更新状态栏进度
                progress_parts = status_info.split()
                for part in progress_parts:
                    if part.startswith("time="):
                        progress_percent = part[5:]
                
                # 获取当前操作
                if hasattr(self, 'last_stage') and self.last_stage:
                    operation = self.last_stage
            elif "fps=" in status_info or "time=" in status_info or "size=" in status_info or "bitrate=" in status_info or "speed=" in status_info:
                # 这是FFmpeg的其他进度信息，只在3秒间隔时记录到日志
                if not hasattr(self, '_last_log_update_time') or (current_time - self._last_log_update_time) >= 3.0:
                    self._last_log_update_time = current_time
                    self.log_message(status_info, "PROGRESS")
            else:
                # 普通进度信息，限制更新频率为3秒一次
                if not hasattr(self, '_last_progress_update') or current_time - self._last_progress_update >= 3.0:
                    self.log_message(f"进度: {status_info}", "PROGRESS")
                    self._last_progress_update = current_time
                
                # 如果是普通状态信息，则更新当前操作标签（仅显示简洁信息）
                # 提取操作核心部分，不显示详细参数
                operation = status_info
                if "..." in operation:
                    operation = operation.split("...")[0].strip() + "..."
                if "(" in operation:
                    operation = operation.split("(")[0].strip() + "..."
                    
                self.current_operation_label.config(text=f"当前操作: {operation}")
                
                # 更新状态栏中的剩余文件夹和音频文件信息
                if "剩余文件夹:" in status_info and "剩余音频:" in status_info:
                    # 从状态信息中提取剩余数量
                    try:
                        remaining_folders = status_info.split("剩余文件夹:")[1].split(",")[0].strip()
                        remaining_audio = status_info.split("剩余音频:")[1].split("]")[0].strip() if "]" in status_info else status_info.split("剩余音频:")[1].strip()
                        remaining_info = f"剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio}"
                    except:
                        pass
            
        if value is not None:
            # 将进度更新到日志
            progress_text = f"当前进度: {int(value)}%"
            if not hasattr(self, '_last_percent_update') or self._last_percent_update != int(value):
                self.log_message(progress_text, "PROGRESS")
                self._last_percent_update = int(value)
            
            if not progress_percent:
                progress_percent = f"{int(value)}%"
        
        # 根据进度更新阶段描述
        if value is not None:
            self.update_progress_stage(value)
            if hasattr(self, 'last_stage') and self.last_stage and not operation:
                operation = self.last_stage
        
        # 组合状态信息并更新状态栏
        status_parts = []
        if operation:
            status_parts.append(operation)
        if progress_percent:
            status_parts.append(f"进度: {progress_percent}")
        if remaining_info:
            status_parts.append(remaining_info)
        if time_info:
            status_parts.append(time_info)
        
        # 将所有状态信息组合在一起显示
        combined_status = " | ".join(status_parts)
        if combined_status:
            self.status_display.config(text=combined_status)
            
        # 确保GUI即时更新
        self.root.update_idletasks()
    
    def update_progress_stage(self, value):
        """根据进度值更新进度阶段描述"""
        progress_stages = [
            (0, "准备环境..."),
            (5, "扫描文件..."),
            (10, "选择视频背景..."),
            (20, "处理原始视频..."),
            (30, "创建循环视频..."),
            (40, "处理音频..."),
            (50, "合成视频..."),
            (60, "验证视频文件..."),
            (65, "预处理视频文件..."),
            (70, "修复音频编码..."),
            (75, "准备合并列表..."),
            (80, "准备编码参数..."),
            (85, "合并视频文件..."),
            (90, "后期处理..."),
            (95, "执行字幕添加..."),
            (100, "处理完成!")
        ]
        
        # 根据当前进度找到对应的阶段描述
        stage_description = "处理中..."
        for stage_value, description in progress_stages:
            if value <= stage_value:
                stage_description = description
                break
        
        # 在日志中添加阶段变化的记录（只在阶段变化时记录）
        if not hasattr(self, 'last_stage'):
            self.last_stage = ""
            
        if self.last_stage != stage_description:
            self.log_message(f"进度阶段: {stage_description}", "PROGRESS")
            self.last_stage = stage_description
    
    def log_message(self, message, level="INFO"):
        """向日志窗口添加消息"""
        self.log_text.config(state=tk.NORMAL)
        
        # 获取当前时间
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 检查日志数量是否超过300条，如果超过则删除最早的一条
        if self.log_text.get("1.0", tk.END).count('\n') > 500:
            # 获取第一行的结束位置
            first_line_end = self.log_text.index("1.0 lineend+1c")
            # 删除第一行
            self.log_text.delete("1.0", first_line_end)
        
        # 根据日志级别设置标签颜色
        if level == "ERROR":
            tag = "error"
            prefix = "[错误] "
            color = "#FF5252"  # 红色
        elif level == "WARNING":
            tag = "warning"
            prefix = "[警告] "
            color = "#FFA726"  # 橙色
        elif level == "SUCCESS":
            tag = "success"
            prefix = "[成功] "
            color = "#66BB6A"  # 绿色
        elif level == "PROGRESS":
            tag = "progress"
            prefix = "[进度] "
            color = "#42A5F5"  # 蓝色
            
            # 对FFmpeg进度信息特殊处理
            if "frame=" in message:
                # 将FFmpeg的进度信息保持原样，只添加时间戳和进度前缀
                full_message = f"[{timestamp}] {prefix}{message}\n"
                self.log_text.insert(tk.END, full_message)
                
                # 为进度信息添加标签
                start_index = self.log_text.index(f"end - {len(full_message)} chars")
                end_index = self.log_text.index("end - 1 chars")  # 不包括换行符
                
                # 创建颜色标签（如果不存在）
                if not tag in self.log_text.tag_names():
                    self.log_text.tag_configure(tag, foreground=color)
                
                # 应用标签
                self.log_text.tag_add(tag, start_index, end_index)
                
                # 滚动到最新消息
                self.log_text.see(tk.END)
                self.log_text.config(state=tk.DISABLED)
                
                # 确保GUI即时更新
                self.root.update_idletasks()
                return
        else:  # INFO
            tag = "info"
            prefix = "[信息] "
            color = "#757575"  # 灰色
        
        # 添加带时间戳和级别的消息
        full_message = f"[{timestamp}] {prefix}{message}\n"
        self.log_text.insert(tk.END, full_message)
        
        # 为不同级别的消息添加不同的颜色标签
        start_index = self.log_text.index(f"end - {len(full_message)} chars")
        end_index = self.log_text.index("end - 1 chars")  # 不包括换行符
        
        # 创建颜色标签（如果不存在）
        if not tag in self.log_text.tag_names():
            self.log_text.tag_configure(tag, foreground=color)
        
        # 应用标签
        self.log_text.tag_add(tag, start_index, end_index)
        
        # 滚动到最新消息
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 确保GUI即时更新
        self.root.update_idletasks()
    
    def update_subtitle_style(self, style_dict):
        """更新字幕样式设置"""
        self.subtitle_settings = style_dict
        
        # 生成样式描述文本
        style_desc = f"{style_dict['font']} {style_dict['font_size']}pt"
        if style_dict['bold']:
            style_desc += " 粗体"
        if style_dict['italic']:
            style_desc += " 斜体"
            
        # 更新UI显示
        self.subtitle_style_label.config(text=f"字幕样式: {style_desc}")
        self.param_subtitle_label.config(text=f"字幕: 添加 ({style_dict['position']})")
        self.log_message(f"已设置字幕样式: {style_desc}")

    def open_subtitle_settings(self):
        """打开字幕样式设置对话框"""
        dialog = SubtitleSettingsDialog(self.root, self.subtitle_settings, self.update_subtitle_style)
        self.root.wait_window(dialog.window)
    
    def start_processing(self):
        if not self.process_folder_path:
            messagebox.showwarning("警告", "请选择处理文件夹！")
            return
        
        if not self.valid_subfolders:
            messagebox.showwarning("警告", "未检测到有效的子文件夹！")
            return
        
        if self.add_bgm_var.get() and not self.bgm_path:
            messagebox.showwarning("警告", "已勾选添加BGM，请选择BGM路径！")
            return
        
        if self.add_subtitle_var.get() and not self.subtitle_settings:
            messagebox.showwarning("警告", "已勾选添加字幕，请先设置字幕样式！")
            return
            
        # 清空日志窗口
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 更新UI状态
        self.start_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.current_operation_label.config(text="当前操作: 准备中...")
        self.last_stage = "准备中..."
        
        # 记录开始时间
        self.start_time = time.time()
        
        # 开始定时器，每秒更新一次状态
        self.start_status_timer()
        
        # 更新字幕参数显示
        if self.add_subtitle_var.get():
            self.param_subtitle_label.config(text=f"字幕: 添加 ({self.subtitle_style_var.get()})")
        else:
            self.param_subtitle_label.config(text="字幕: 不添加")
        
        # 更新合并视频参数显示
        if self.create_merged_video_var.get():
            self.param_merged_label.config(text="合并视频: 创建")
        else:
            self.param_merged_label.config(text="合并视频: 不创建")
        
        # 更新分辨率参数显示
        if self.custom_resolution_var.get():
            self.param_resolution_label.config(text=f"分辨率: 自定义({self.resolution_width_var.get()}x{self.resolution_height_var.get()})")
        else:
            self.param_resolution_label.config(text="分辨率: 默认(1280x720)")
        
        # 更新码率参数显示
        if self.custom_bitrate_var.get():
            self.param_bitrate_label.config(text=f"码率: 自定义({self.bitrate_var.get()}k)")
        else:
            self.param_bitrate_label.config(text="码率: 默认(2000k)")
        
        # 更新状态
        self.update_status(f"准备开始... | 剩余文件夹: {len(self.valid_subfolders)}, 剩余音频: 计算中... | 耗时: 00:00:00")
        
        self.log_message("开始处理视频...", "SUCCESS")
        
        # 记录选项设置，只记录勾选的选项
        self.log_message("处理设置:", "INFO")
        self.log_message(f"  - 并行处理: {'启用' if self.parallel_processing_var.get() else '禁用'}, 最大线程数: {self.max_workers_var.get()}", "INFO")
        self.log_message(f"  - 硬件加速: 启用", "INFO")
        
        # 只在勾选BGM时显示BGM相关设置
        if self.add_bgm_var.get():
            self.log_message(f"  - 添加BGM: 是", "INFO")
            self.log_message(f"  - BGM路径: {os.path.basename(self.bgm_path)}", "INFO")
            self.log_message(f"  - BGM音量: {int(self.bgm_volume_var.get() * 100)}%", "INFO")
            
            # 添加BGM模式信息
            if self.bgm_mode_var.get() == 1:
                self.log_message(f"  - BGM模式: 单文件循环", "INFO")
            else:
                self.log_message(f"  - BGM模式: 文件夹随机", "INFO")
        
        # 只在勾选字幕时显示字幕相关设置
        if self.add_subtitle_var.get():
            self.log_message(f"  - 添加字幕: 是", "INFO")
            if hasattr(self, 'subtitle_style_var') and self.subtitle_style_var.get():
                self.log_message(f"  - 字幕样式: {self.subtitle_style_var.get()}", "INFO")
        
        # 只在勾选合并视频时显示相关设置
        if self.create_merged_video_var.get():
            self.log_message(f"  - 创建合并视频: 是", "INFO")
        
        # 只在勾选合并原视频时显示相关设置
        if self.merge_original_video_var.get():
            self.log_message(f"  - 合并原视频与第一集: 是", "INFO")
        
        # 记录分辨率和码率设置
        if self.custom_resolution_var.get():
            self.log_message(f"  - 分辨率: 自定义({self.resolution_width_var.get()}x{self.resolution_height_var.get()})", "INFO")
        
        if self.custom_bitrate_var.get():
            self.log_message(f"  - 码率: 自定义({self.bitrate_var.get()}k)", "INFO")
        
        # 准备处理选项
        options = {
            'output_dir': self.process_folder_path,  # 确保将输出目录传递给处理线程
            'add_bgm': self.add_bgm_var.get(),
            'bgm_path': self.bgm_path if self.add_bgm_var.get() else None,
            'bgm_volume': self.bgm_volume_var.get(),  # 使用用户设置的BGM音量
            'bgm_mode': 'loop' if self.bgm_mode_var.get() == 1 else 'random',  # 添加BGM模式
            'add_subtitles': self.add_subtitle_var.get(),
            'subtitle_style': self.subtitle_settings if self.add_subtitle_var.get() else None,
            'hardware_acceleration': True,  # 默认启用硬件加速
            'create_merged_video': self.create_merged_video_var.get(),
            'merge_original_video': self.merge_original_video_var.get(),  # 新增：是否合并原视频与第一集
            'parallel_processing': self.parallel_processing_var.get(),  # 添加并行处理选项
            'max_workers': self.max_workers_var.get() if self.parallel_processing_var.get() else 1,  # 最大线程数
            'clean_temp_files': True,  # 默认情况下清理临时文件
            'custom_resolution': self.custom_resolution_var.get(),  # 是否使用自定义分辨率
            'resolution_width': self.resolution_width_var.get() if self.custom_resolution_var.get() else '1280',  # 默认宽度
            'resolution_height': self.resolution_height_var.get() if self.custom_resolution_var.get() else '720',  # 默认高度
            'custom_bitrate': self.custom_bitrate_var.get(),  # 是否使用自定义码率
            'bitrate': self.bitrate_var.get() if self.custom_bitrate_var.get() else '2000',  # 默认码率，单位为k
        }
        
        # 创建并启动处理线程
        self.processing_thread = VideoProcessingThread(
            self.process_folder_path, 
            self.valid_subfolders, 
            options, 
            self.update_progress, 
            self.process_completed
        )
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        # 记录处理开始
        self.log_message(f"处理线程已启动，共有 {len(self.valid_subfolders)} 个子文件夹需要处理", "SUCCESS")
    
    def start_status_timer(self):
        """启动状态更新定时器"""
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
        
        self.update_status_display()
        
    def update_status_display(self):
        """更新状态显示"""
        if hasattr(self, 'start_time'):
            # 计算已用时间
            elapsed = time.time() - self.start_time
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_info = f"耗时: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            
            # 获取当前显示的状态文本
            current_text = self.status_display.cget("text")
            
            # 更新耗时部分
            if "耗时:" in current_text:
                parts = current_text.split(" | ")
                updated_parts = []
                for part in parts:
                    if part.startswith("耗时:"):
                        updated_parts.append(time_info)
                    else:
                        updated_parts.append(part)
                
                new_text = " | ".join(updated_parts)
                self.status_display.config(text=new_text)
            else:
                # 如果当前没有耗时信息，添加到末尾
                if current_text:
                    self.status_display.config(text=f"{current_text} | {time_info}")
                else:
                    self.status_display.config(text=time_info)
        
        # 每3秒更新一次
        self.timer_id = self.root.after(3000, self.update_status_display)
    
    def cancel_processing(self):
        """取消处理"""
        if self.processing_thread and self.processing_thread.is_alive():
            self.log_message("正在取消处理...")
            
            # 设置取消标志
            if hasattr(self.processing_thread, 'cancel'):
                self.processing_thread.cancel()
            
            # 禁用取消按钮防止多次点击
            self.cancel_button.config(state=tk.DISABLED)
            
            # 更新状态
            self.update_status("正在取消处理...")
            self.current_operation_label.config(text="当前操作: 正在取消...")
            
            # 停止定时器
            if self.timer_id:
                self.root.after_cancel(self.timer_id)
                self.timer_id = None
            
        else:
            # 如果线程不存在或已结束，直接重置UI
            self.reset_ui_after_processing()
            
    def reset_ui_after_processing(self):
        """处理完成后重置UI"""
        # 重置按钮状态
        self.start_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        
        # 更新状态
        self.current_operation_label.config(text="当前操作: 就绪")
        
        # 停止定时器
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
    
    def process_completed(self, success, message):
        """处理完成后的回调函数"""
        # 添加静态标志位防止重复弹窗
        if hasattr(VideoProcessingApp, '_completion_dialog_shown') and VideoProcessingApp._completion_dialog_shown:
            # 如果已经显示过完成弹窗，则不再显示
            self.log_message("已显示过完成弹窗，跳过重复显示", "INFO")
            return
        
        # 设置标志位表示已经显示过弹窗
        VideoProcessingApp._completion_dialog_shown = True
        
        # 停止定时器
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
            
        if success:
            self.log_message(message, "SUCCESS")
            
            # 计算总用时
            time_taken = time.time() - self.start_time if hasattr(self, 'start_time') else 0
            hours, remainder = divmod(time_taken, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            # 显示处理完成弹窗
            messagebox.showinfo("处理完成", f"所有视频处理已完成！\n总处理时间: {time_str}")
        else:
            # 显示处理失败弹窗
            messagebox.showerror("处理失败", f"视频处理失败：\n{message}")
        
        # 重置UI状态
        self.reset_ui_after_processing()
        
        # 设置定时器在1秒后重置标志位，允许下次处理时显示弹窗
        self.root.after(1000, self._reset_completion_dialog_flag)
    
    def _reset_completion_dialog_flag(self):
        """重置完成弹窗标志位，允许下次处理时显示弹窗"""
        VideoProcessingApp._completion_dialog_shown = False
        self.log_message("重置完成弹窗标志位", "DEBUG")
    
    def update_volume_label(self, value):
        """更新BGM音量标签显示"""
        # 将浮点数转换为百分比显示
        percent = int(float(value) * 100)
        self.bgm_volume_label.config(text=f"{percent}%")
        
        # 更新参数显示（如果已选择BGM）
        if self.add_bgm_var.get() and self.bgm_path:
            basename = os.path.basename(self.bgm_path)
            if self.bgm_mode_var.get() == 1:  # 单文件模式
                self.param_bgm_label.config(text=f"BGM: {basename} (音量: {percent}%)")
            else:  # 文件夹模式
                self.param_bgm_label.config(text=f"BGM: {basename}文件夹 (音量: {percent}%)")

    def show_log_context_menu(self, event):
        """显示日志右键菜单"""
        self.log_context_menu.post(event.x_root, event.y_root)
    
    def copy_log_selection(self):
        """复制选中的日志文本"""
        try:
            selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
        except tk.TclError:
            # 如果没有选中文本，则不执行任何操作
            pass
    
    def copy_all_logs(self):
        """复制所有日志"""
        all_text = self.log_text.get(1.0, tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(all_text)
        
    def clear_logs(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(tk.END, "日志已清空\n")
        self.log_text.config(state=tk.DISABLED)

    # ------------------------------------------------------------------
    # 预设功能实现
    # ------------------------------------------------------------------
    def load_presets(self):
        """从磁盘读取预设文件"""
        try:
            if os.path.exists(PRESET_FILE):
                with open(PRESET_FILE, 'r', encoding='utf-8') as f:
                    self.presets = json.load(f)
        except Exception as e:
            print(f"读取预设文件失败: {e}")
            self.presets = {}

    def save_presets(self):
        """将当前 self.presets 保存到磁盘"""
        try:
            with open(PRESET_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.presets, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存预设文件失败: {e}")

    def refresh_preset_combo(self):
        """刷新下拉框显示"""
        if hasattr(self, 'preset_combo'):
            self.preset_combo['values'] = list(self.presets.keys())

    def build_current_preset(self):
        """收集当前 UI 选项，返回预设字典"""
        return {
            'add_bgm': self.add_bgm_var.get(),
            'bgm_path': self.bgm_path,
            'bgm_mode': self.bgm_mode_var.get(),
            'bgm_volume': self.bgm_volume_var.get(),
            'add_subtitles': self.add_subtitle_var.get(),
            'subtitle_settings': self.subtitle_settings,
            'create_merged_video': self.create_merged_video_var.get(),
            'merge_original_video': self.merge_original_video_var.get(),
            'parallel_processing': self.parallel_processing_var.get(),
            'max_workers': self.max_workers_var.get(),
            'custom_resolution': self.custom_resolution_var.get(),
            'resolution_width': self.resolution_width_var.get(),
            'resolution_height': self.resolution_height_var.get(),
            'custom_bitrate': self.custom_bitrate_var.get(),
            'bitrate': self.bitrate_var.get()
        }

    def save_current_as_preset(self):
        """弹出对话框获取名称并保存当前设置为预设"""
        name = simpledialog.askstring("保存预设", "请输入预设名称：", parent=self.root)
        if not name:
            return
        # 覆盖或新增
        self.presets[name] = self.build_current_preset()
        self.save_presets()
        self.refresh_preset_combo()
        messagebox.showinfo("保存成功", f"已保存预设: {name}")

    def apply_selected_preset(self, event=None):
        """根据用户选择的预设填充界面各项"""
        name = self.preset_combo.get()
        if name not in self.presets:
            return
        p = self.presets[name]

        # 恢复各项设置
        self.add_bgm_var.set(p.get('add_bgm', False))
        self.bgm_path = p.get('bgm_path', '')
        self.bgm_path_label.config(text=os.path.basename(self.bgm_path) if self.bgm_path else "未选择BGM")

        self.bgm_mode_var.set(p.get('bgm_mode', 1))
        self.bgm_volume_var.set(p.get('bgm_volume', 0.2))
        self.update_volume_label(self.bgm_volume_var.get())

        self.add_subtitle_var.set(p.get('add_subtitles', False))
        self.subtitle_settings = p.get('subtitle_settings', None)
        if self.subtitle_settings:
            self.subtitle_style_label.config(text="字幕样式: 已设置")
        else:
            self.subtitle_style_label.config(text="字幕样式: 未设置")

        self.create_merged_video_var.set(p.get('create_merged_video', False))
        self.merge_original_video_var.set(p.get('merge_original_video', False))

        self.parallel_processing_var.set(p.get('parallel_processing', True))
        self.max_workers_var.set(p.get('max_workers', min(4, os.cpu_count() or 4)))

        self.custom_resolution_var.set(p.get('custom_resolution', False))
        self.resolution_width_var.set(p.get('resolution_width', '1280'))
        self.resolution_height_var.set(p.get('resolution_height', '720'))

        self.custom_bitrate_var.set(p.get('custom_bitrate', False))
        self.bitrate_var.set(p.get('bitrate', '2000'))

        # 更新日志和状态
        self.log_message(f"已应用预设: {name}", "SUCCESS")

    # ---------------- 预设删除方法 ----------------
    def delete_selected_preset(self):
        """删除当前下拉框选中的预设"""
        name = self.preset_combo.get()
        if not name:
            messagebox.showwarning("提示", "请选择需要删除的预设！")
            return
        if messagebox.askyesno("确认删除", f"确定删除预设: {name} ?"):
            if name in self.presets:
                del self.presets[name]
                self.save_presets()
                self.refresh_preset_combo()
                self.preset_combo.set("")
                messagebox.showinfo("删除成功", f"已删除预设: {name}")
            else:
                messagebox.showwarning("提示", "预设不存在！")

    # ------------------------------------------------------------------
    # 关闭应用时的清理逻辑
    # ------------------------------------------------------------------
    def on_close(self):
        """处理窗口关闭事件，安全终止后台线程和 FFmpeg 进程"""
        try:
            # 若仍有处理线程在运行，提醒用户
            if self.processing_thread and self.processing_thread.is_alive():
                if not messagebox.askyesno("正在处理", "当前仍有任务在运行，确定要强制退出并终止 FFmpeg 吗？"):
                    return
                # 取消线程并等待
                try:
                    self.processing_thread.cancel()
                except Exception as e:
                    print(f"取消处理线程时出错: {e}")
                # 等待最多 5 秒
                self.processing_thread.join(timeout=5)

            # 终止残留 FFmpeg
            self._terminate_ffmpeg()
        finally:
            # 最终销毁窗口
            self.root.destroy()

    def _terminate_ffmpeg(self):
        """强制结束系统中所有 ffmpeg / ffprobe 进程（仅限本工具启动的）"""
        try:
            if platform.system() == "Windows":
                # /T 终止进程树，/F 强制
                subprocess.run(["taskkill", "/F", "/T", "/IM", "ffmpeg.exe"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run(["taskkill", "/F", "/T", "/IM", "ffprobe.exe"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            else:
                # Unix 系统使用 pkill
                subprocess.run(["pkill", "-9", "ffmpeg"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run(["pkill", "-9", "ffprobe"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print("已尝试终止所有 FFmpeg 相关进程")
        except Exception as e:
            print(f"终止 FFmpeg 进程时出错: {e}")


class Logger:
    def __init__(self, logging_level=logging.INFO):
        self.level = logging_level
        # 配置日志格式
        logging.basicConfig(
            level=logging_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
    def info(self, message):
        logging.info(message)
        
    def warning(self, message):
        logging.warning(message)
        
    def error(self, message):
        logging.error(message)
        
    def debug(self, message):
        logging.debug(message)


def check_ffmpeg():
    """检查系统中是否安装了FFmpeg"""
    try:
        if platform.system() == 'Windows':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            # 使用CREATE_NO_WINDOW标志
            creation_flags = subprocess.CREATE_NO_WINDOW
            
            subprocess.run(
                ["ffmpeg", "-version"], 
                startupinfo=startupinfo, 
                creationflags=creation_flags,
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                check=True
            )
        else:
            subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False


# 添加隐藏控制台窗口的函数
def hide_console():
    """隐藏控制台窗口（仅在Windows系统下有效）"""
    if sys.platform == "win32":
        try:
            import ctypes
            hwnd = ctypes.windll.kernel32.GetConsoleWindow()
            if hwnd != 0:
                ctypes.windll.user32.ShowWindow(hwnd, 0)  # SW_HIDE = 0
        except Exception as e:
            print(f"隐藏控制台窗口失败: {e}")


# 添加获取隐藏窗口启动信息的辅助函数
def get_hidden_startupinfo():
    """获取隐藏控制台窗口的启动信息（仅在Windows下有效）"""
    if platform.system() == 'Windows':
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        
        # 设置进程创建标志，完全分离控制台
        creation_flags = subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
        return startupinfo, creation_flags
    else:
        return None, 0


# 预设保存文件路径（与程序同目录）
PRESET_FILE = "parameter_presets.json"

def run():
    # 检查依赖
    if not check_ffmpeg():
        sys.exit(1)
    
    # 隐藏控制台窗口（仅在Windows平台执行）
    hide_console()
    
    # 创建主窗口
    root = tk.Tk()
    
    # 设置应用程序图标和任务栏图标
    try:
        # 尝试设置应用程序图标
        if platform.system() == "Windows":
            # 在Windows上使用ico格式
            root.iconbitmap(default="icon.ico")
    except:
        # 如果图标设置失败，忽略并继续
        pass
    
    # 设置ttk主题 - 使用系统主题
    style = ttk.Style()
    
    # 在Windows 10/11上使用现代主题
    if platform.system() == "Windows":
        try:
            style.theme_use("vista")
        except:
            # 如果vista主题不可用，使用clam主题
            style.theme_use("clam")
    else:
        # 在其他平台上尝试使用clam主题
        try:
            style.theme_use("clam")
        except:
            pass
    
    # 创建应用实例
    app = VideoProcessingApp(root)
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    run()



