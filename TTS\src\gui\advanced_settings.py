"""
高级设置界面模块，用于显示和管理高级配音设置
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
import time

class AdvancedSettingsFrame(ttk.Frame):
    """高级设置界面，显示高级配音设置"""

    def __init__(self, parent):
        """
        初始化高级设置界面
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 设置密码
        self.password = "1294174256"
        
        # 设置是否已解锁
        self.is_unlocked = False
        
        # 解锁的有效时间（秒）
        self.unlock_duration = 300  # 5分钟
        
        # 解锁的过期时间
        self.unlock_expiry = 0
        
        # 1. 初始化 settings 字典的默认值
        self._initialize_default_settings_dict()
        
        # 2. 初始化Tkinter变量 (使用默认的 self.settings 值)
        self.init_variables()
        
        # 3. 尝试从文件加载设置，如果成功则更新 self.settings 和 Tkinter 变量
        self._load_settings_from_file_and_update_ui_vars()
        
        # 4. 创建界面组件
        self.create_widgets()
        
        # 5. 初始时检查锁定状态并更新界面
        self.update_ui_lock_status()
        
    def _initialize_default_settings_dict(self):
        """初始化高级设置的默认字典 self.settings 及 UI 相关的 option 列表"""
        # 默认设置
        self.settings = {
            # 处理选项
            "concurrent_tasks": 6,  # 并发任务数
            "retry_count": 5,  # 重试次数
            "retry_interval": 10,  # 重试间隔（秒）
            "batch_size": 3000,  # 分批大小（字符数），修改为3000
            # 文本处理
            "remove_empty_lines": True,  # 去除空行
        }
        
        # 并发任务数选项列表
        self.concurrent_tasks_options = [
            "2",    # 2个任务
            "3",    # 3个任务
            "5",    # 5个任务
            "6",    # 6个任务
            "8",    # 8个任务
            "10",   # 10个任务
            "12",   # 12个任务
            "16",   # 16个任务
            "20",   # 20个任务
        ]
        
        # 重试次数选项列表
        self.retry_count_options = [
            "5",    # 5次
            "10",   # 10次
            "15",   # 15次
            "20",   # 20次
        ]
        
    def init_variables(self):
        """初始化Tkinter变量"""
        # 处理选项变量
        self.concurrent_var = tk.StringVar(value=str(self.settings["concurrent_tasks"]))
        self.retry_count_var = tk.StringVar(value="无限" if self.settings["retry_count"] == -1 else str(self.settings["retry_count"]))
        self.retry_interval_var = tk.IntVar(value=self.settings["retry_interval"])
        self.batch_size_var = tk.IntVar(value=self.settings["batch_size"])
        self.remove_empty_var = tk.BooleanVar(value=self.settings["remove_empty_lines"])
        
        # 锁定状态变量
        self.lock_status_var = tk.StringVar(value="🔒 设置已锁定")
        
    def _load_settings_from_file_and_update_ui_vars(self):
        """
        从文件加载设置。
        如果成功，则更新 self.settings 字典，并接着更新对应的Tkinter UI变量。
        """
        try:
            settings_dir = os.path.expanduser("~/.tts_tool")
            # 新的配置文件名
            settings_file = os.path.join(settings_dir, "tts_advanced_config.json")

            # 检查是否存在旧的配置文件，优先使用新文件
            old_settings_file = os.path.join(settings_dir, "advanced_settings.json")

            if os.path.exists(settings_file):
                # 使用新配置文件
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)
            elif os.path.exists(old_settings_file):
                # 如果新文件不存在但旧文件存在，加载旧文件并迁移
                with open(old_settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)
                print("检测到旧的高级设置文件，将在下次保存时迁移到新文件名")
            else:
                # 两个文件都不存在，使用默认设置
                loaded_settings = {}

            # 更新 self.settings 字典
            for key, value in loaded_settings.items():
                if key in self.settings:
                    self.settings[key] = value

            # self.settings 已更新，现在用这些值更新 Tkinter 变量
            # (这些变量已在 self.init_variables() 中基于默认值创建)
            self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
            self.retry_count_var.set("无限" if self.settings["retry_count"] == -1 else str(self.settings["retry_count"]))
            self.retry_interval_var.set(self.settings["retry_interval"])
            self.batch_size_var.set(self.settings["batch_size"])
            self.remove_empty_var.set(self.settings["remove_empty_lines"])

            if loaded_settings:
                print("成功从文件加载高级设置并更新了UI变量。")
            else:
                print("高级设置文件不存在，将使用默认设置。")
        except Exception as e:
            print(f"加载高级设置文件或更新UI变量时发生错误: {str(e)}")
            # 如果加载或更新UI变量失败，Tkinter变量将保留它们在init_variables()中设置的初始值（基于默认settings）。
            # self.settings 字典可能部分更新或保持默认。
            pass

    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(10, 20))
        
        ttk.Label(title_frame, text="高级设置", 
                 font=("微软雅黑", 16, "bold"),
                 foreground="#1565C0").pack(anchor=tk.CENTER)
        
        # 分隔线
        ttk.Separator(self, orient="horizontal").pack(fill=tk.X, padx=20, pady=5)
        
        # 设置区域容器 - 使用居中布局
        self.main_container = ttk.Frame(self)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=50, pady=10)
        
        # 创建设置组 - 处理选项
        process_frame = self.create_section_frame("处理选项")
        
        # 批次大小设置（锁定状态）
        ttk.Label(process_frame, text="批次大小(字):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.batch_entry = ttk.Entry(process_frame, textvariable=self.batch_size_var, width=12, state="readonly")
        self.batch_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 并发任务数设置（改为下拉列表）
        ttk.Label(process_frame, text="并发任务数(最大20):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.tasks_combo = ttk.Combobox(process_frame, textvariable=self.concurrent_var, 
                                      values=self.concurrent_tasks_options, 
                                      state="readonly", width=10)
        self.tasks_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 添加并发任务数下拉框的绑定，当用户尝试选择时检查锁定状态
        self.tasks_combo.bind("<<ComboboxSelected>>", lambda event: self.on_concurrent_tasks_change(event))
        
        # 锁定状态显示和解锁按钮
        lock_frame = ttk.Frame(process_frame)
        lock_frame.grid(row=0, column=2, rowspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 锁定状态标签
        lock_status = ttk.Label(lock_frame, textvariable=self.lock_status_var)
        lock_status.pack(anchor=tk.W, pady=(0, 5))
        
        # 解锁/锁定按钮
        self.lock_btn = ttk.Button(lock_frame, text="解锁设置", width=8, command=self.toggle_lock)
        self.lock_btn.pack(anchor=tk.W)
        
        # 创建设置组 - 文本处理选项
        text_frame = self.create_section_frame("文本处理")
        
        # 去除空行选项
        remove_empty_check = ttk.Checkbutton(text_frame, text="去除空行", 
                                          variable=self.remove_empty_var)
        remove_empty_check.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5, columnspan=2)
        
        # 按钮区域
        button_frame = ttk.Frame(self.main_container)
        button_frame.pack(fill=tk.X, pady=20)
        
        # 保存设置按钮
        save_btn = ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=15)
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 重置设置按钮
        reset_btn = ttk.Button(button_frame, text="重置设置", command=self.reset_settings, width=15)
        reset_btn.pack(side=tk.RIGHT, padx=5)
    
    def toggle_lock(self):
        """切换锁定状态"""
        current_time = time.time()
        
        # 如果已解锁但已过期，重置解锁状态
        if self.is_unlocked and current_time > self.unlock_expiry:
            self.is_unlocked = False
            
        if self.is_unlocked:
            # 如果已解锁，则锁定
            self.lock_settings()
        else:
            # 如果已锁定，则尝试解锁
            self.unlock_settings()
    
    def unlock_settings(self):
        """解锁设置"""
        # 弹出密码输入对话框
        password = simpledialog.askstring("密码", 
                                        "请输入密码以解锁设置:", 
                                        show="*")
        
        # 验证密码
        if password == self.password:
            # 密码正确，解锁设置
            self.is_unlocked = True
            self.unlock_expiry = time.time() + self.unlock_duration
            
            # 更新UI
            self.update_ui_lock_status()
            
            # 显示解锁成功消息
            messagebox.showinfo("解锁成功", f"设置已解锁，{int(self.unlock_duration/60)}分钟内可以自由修改")
        else:
            # 密码错误
            messagebox.showerror("错误", "密码错误，无法解锁设置")
    
    def lock_settings(self):
        """锁定设置"""
        # 锁定状态
        self.is_unlocked = False
        
        # 更新UI
        self.update_ui_lock_status()
    
    def check_lock_status(self):
        """检查锁定状态，如果已过期则自动锁定"""
        if self.is_unlocked and time.time() > self.unlock_expiry:
            self.is_unlocked = False
            # 更新UI显示
            self.update_ui_lock_status()
            
        # 返回当前锁定状态
        return self.is_unlocked
        
    def create_section_frame(self, title):
        """创建带标题的设置分组"""
        # 创建分组容器
        section_frame = ttk.LabelFrame(self.main_container, text=title)
        section_frame.pack(fill=tk.X, pady=(10, 15), padx=5)
        
        return section_frame
    
    def on_setting_change(self, *args):
        """当设置改变时回调这个函数"""
        # 检查锁定状态
        if not self.check_lock_status():
            return
            
        # 处理重试次数的特殊情况
        retry_value = self.retry_count_var.get()
        if retry_value == "无限":
            retry_count = -1
        else:
            try:
                retry_count = int(retry_value)
            except ValueError:
                retry_count = 5
                
        # 处理并发任务数，确保转换为整数并限制最大值为20
        try:
            concurrent_tasks = int(self.concurrent_var.get())
            # 限制最大值为20
            if concurrent_tasks > 20:
                concurrent_tasks = 20
                self.concurrent_var.set(str(concurrent_tasks))
                messagebox.showinfo("提示", "并发任务数已限制为最大值20")
        except ValueError:
            concurrent_tasks = 6  # 默认值
            self.concurrent_var.set(str(concurrent_tasks))
        
        # 更新设置
        self.settings.update({
            "concurrent_tasks": concurrent_tasks,
            "retry_count": retry_count,
            "retry_interval": self.retry_interval_var.get(),
            "batch_size": self.batch_size_var.get(),
            "remove_empty_lines": self.remove_empty_var.get(),
        })
        
        return self.settings
    
    def on_concurrent_tasks_change(self, event=None):
        """处理并发任务数变更"""
        # 检查锁定状态
        if not self.check_lock_status():
            # 如果未解锁，弹出提示并还原值
            messagebox.showinfo("提示", "请先解锁设置再修改并发任务数")
            # 还原为之前的值
            self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
            return
        
        # 如果已解锁，则允许更改并更新设置
        self.on_setting_change()
    
    def save_settings(self):
        """保存设置到文件"""
        # 更新设置
        self.on_setting_change()
        
        try:
            # 获取设置文件路径
            settings_dir = os.path.expanduser("~/.tts_tool")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            # 新的配置文件名
            settings_file = os.path.join(settings_dir, "tts_advanced_config.json")

            # 检查是否存在旧的配置文件，如果存在则迁移
            old_settings_file = os.path.join(settings_dir, "advanced_settings.json")
            if os.path.exists(old_settings_file) and not os.path.exists(settings_file):
                try:
                    # 迁移旧配置文件到新文件名
                    import shutil
                    shutil.copy2(old_settings_file, settings_file)
                    print("已迁移旧的高级设置配置文件到新文件名")
                    # 删除旧文件
                    os.remove(old_settings_file)
                    print("已删除旧的配置文件")
                except Exception as e:
                    print(f"迁移配置文件失败: {str(e)}")
                    # 如果迁移失败，仍使用新文件名
            
            # 保存为JSON
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(self.settings, f, indent=4)
            
            # 显示成功消息
            messagebox.showinfo("保存成功", "高级设置已保存")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("保存失败", f"保存设置失败: {str(e)}")
    
    def reset_settings(self):
        """重置高级设置为默认值"""
        if not messagebox.askyesno("确认", "确定要重置所有高级设置为默认值吗？"):
            return
            
        # 重置为默认设置
        self.settings = {
            "concurrent_tasks": 6,
            "retry_count": 5,
            "retry_interval": 10,
            "batch_size": 10000,
            "remove_empty_lines": True,
        }
        
        # 更新UI变量
        self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
        self.retry_count_var.set(str(self.settings["retry_count"]))
        self.retry_interval_var.set(self.settings["retry_interval"])
        self.batch_size_var.set(self.settings["batch_size"])
        self.remove_empty_var.set(self.settings["remove_empty_lines"])
        
        # 更新界面
        self.update_ui_lock_status()
        
        # 显示消息
        messagebox.showinfo("重置成功", "高级设置已重置为默认值")
    
    def get_settings(self):
        """获取当前高级设置"""
        # 更新设置
        settings = {
            "concurrent_tasks": int(self.concurrent_var.get()),
            "retry_count": -1 if self.retry_count_var.get() == "无限" else int(self.retry_count_var.get()),
            "retry_interval": self.retry_interval_var.get(),
            "batch_size": self.batch_size_var.get(),
            "remove_empty_lines": self.remove_empty_var.get(),
        }
        
        return settings

    def update_ui_lock_status(self):
        """检查锁定状态并更新界面"""
        if self.check_lock_status():
            self.batch_entry.configure(state="normal")
            self.tasks_combo.configure(state="readonly")
            self.lock_status_var.set("🔓 设置已解锁")
            self.lock_btn.configure(text="锁定设置")
        else:
            self.batch_entry.configure(state="readonly")
            self.tasks_combo.configure(state="disabled")
            self.lock_status_var.set("🔒 设置已锁定")
            self.lock_btn.configure(text="解锁设置") 