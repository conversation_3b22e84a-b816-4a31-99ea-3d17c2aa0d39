"""
批处理器核心模块，负责批量处理文本文件
"""

# =================== 标准库导入 ===================
import asyncio
import hashlib
import json
import locale
import logging
import multiprocessing
import os
import pickle
import platform
import re
import shutil
import subprocess
import sys
import tempfile
import threading
import time
import uuid
from pathlib import Path

# =================== 第三方库导入 ===================
import aiohttp
import azure.cognitiveservices.speech as speechsdk
import chardet
import edge_tts
import numpy as np
import pygame
import pydub
import pysrt
import requests
import tkinter as tk
from pydub import AudioSegment
from tkinter import filedialog, messagebox, simpledialog, ttk
from tqdm import tqdm

# =================== 配置日志 ===================
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# =================== 文本处理模块 ===================
class TextProcessor:
    """文本处理器类，处理文本加载和预处理"""
    
    def __init__(self):
        """初始化文本处理器"""
        pass
    
    def load_text_file(self, file_path):
        """
        加载文本文件，自动检测编码
        
        参数:
            file_path: 文件路径
            
        返回:
            文件内容文本
        """
        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            # 如果文件过大，只读取前后一部分进行编码检测
            max_detect_size = 1024 * 1024  # 最大检测1MB
            
            # 读取二进制内容进行编码检测
            with open(file_path, 'rb') as f:
                if file_size <= max_detect_size:
                    # 小文件直接全部读取
                    content = f.read()
                else:
                    # 大文件只读取前后各512KB
                    head = f.read(max_detect_size // 2)
                    f.seek(max(0, file_size - max_detect_size // 2))
                    tail = f.read(max_detect_size // 2)
                    content = head + tail
                
            # 检测编码
            result = chardet.detect(content)
            encoding = result['encoding']
            confidence = result['confidence']
            
            # 如果编码检测结果不可靠，使用常见编码尝试读取
            if not encoding or confidence < 0.7:
                # 按照优先级尝试不同编码
                encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'cp936', 'utf-16', 'ascii']
                for enc in encodings_to_try:
                    try:
                        with open(file_path, 'r', encoding=enc, errors='strict') as f:
                            return f.read()
                    except UnicodeDecodeError:
                        continue
                
                # 如果所有尝试都失败，使用检测结果或默认编码，并允许替换错误
                encoding = encoding or 'utf-8'
            
            # 使用检测到的编码重新读取
            with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                return f.read()
                
        except Exception as e:
            print(f"加载文件 {file_path} 失败: {str(e)}")
            raise e
    
    def get_text_file_size(self, file_path):
        """
        获取文本文件的字符数
        
        参数:
            file_path: 文件路径
            
        返回:
            文件字符数
        """
        try:
            # 加载文本
            text = self.load_text_file(file_path)
            
            # 返回字符数
            return len(text)
            
        except Exception as e:
            print(f"获取文件大小失败: {str(e)}")
            raise e
    
    def process_text(self, text, remove_empty_lines=True):
        """
        处理文本，应用预处理规则
        
        参数:
            text: 原始文本
            remove_empty_lines: 是否去除空行
            
        返回:
            处理后的文本
        """
        try:
            # 去除特殊字符
            processed_text = self._remove_special_chars(text)
            
            # 去除空行
            if remove_empty_lines:
                processed_text = self._remove_empty_lines(processed_text)
            
            return processed_text
            
        except Exception as e:
            print(f"处理文本失败: {str(e)}")
            raise e
    
    def _remove_special_chars(self, text):
        """
        去除特殊字符
        
        参数:
            text: 原始文本
            
        返回:
            处理后的文本
        """
        # 替换一些可能导致TTS引擎问题的特殊字符
        result = text
        
        # 替换特殊符号
        replacements = {
            '\u200b': '',  # 零宽空格
            '\u200c': '',  # 零宽非连接符
            '\u200d': '',  # 零宽连接符
            '\u200e': '',  # 左至右标记
            '\u200f': '',  # 右至左标记
            '\ufeff': '',  # 零宽不换行空格
        }
        
        for char, replacement in replacements.items():
            result = result.replace(char, replacement)
        
        return result
    
    def _remove_empty_lines(self, text):
        """
        去除空行
        
        参数:
            text: 原始文本
            
        返回:
            处理后的文本
        """
        # 分割为行
        lines = text.splitlines()
        
        # 过滤空行
        non_empty_lines = [line for line in lines if line.strip()]
        
        # 重新连接
        return '\n'.join(non_empty_lines)
    
    def split_text_by_size(self, text, max_size):
        """
        按照最大字符数分割文本
        
        参数:
            text: 原始文本
            max_size: 最大字符数
            
        返回:
            分割后的文本列表
        """
        # 如果文本长度小于最大大小，直接返回
        if len(text) <= max_size:
            return [text]
            
        # 分割为行
        lines = text.splitlines()
        
        result = []
        current_chunk = []
        current_size = 0
        
        for line in lines:
            line_len = len(line) + 1  # +1 是为了包括换行符
            
            # 如果单行就超过最大大小，需要单独处理这一行
            if line_len > max_size:
                # 如果当前块有内容，先添加当前块
                if current_chunk:
                    result.append('\n'.join(current_chunk))
                    current_chunk = []
                    current_size = 0
                
                # 按字符切分超长行
                remaining_line = line
                while len(remaining_line) > max_size:
                    # 取最大大小的前缀
                    part = remaining_line[:max_size]
                    result.append(part)
                    remaining_line = remaining_line[max_size:]
                
                # 处理剩余部分
                if remaining_line:
                    current_chunk = [remaining_line]
                    current_size = len(remaining_line) + 1
                continue
            
            # 如果当前行加上当前块的大小超过最大大小，保存当前块并开始新块
            if current_size + line_len > max_size and current_chunk:
                result.append('\n'.join(current_chunk))
                current_chunk = [line]
                current_size = line_len
            else:
                current_chunk.append(line)
                current_size += line_len
        
        # 添加最后一个块
        if current_chunk:
            result.append('\n'.join(current_chunk))
        
        # 检查每个分割块的字符数
        for i, chunk in enumerate(result):
            if len(chunk) > max_size * 1.1:  # 允许有10%的误差
                print(f"警告: 分割块 {i+1} 超出目标大小: {len(chunk)} > {max_size}")
        
        return result
    
    @staticmethod
    def check_ffmpeg():
        """
        检查系统中是否安装了ffmpeg
        
        返回:
            (bool, str): 是否安装, 错误信息
        """
        # 检查ffmpeg是否在系统PATH中
        ffmpeg_command = "ffmpeg"
        if platform.system() == "Windows":
            ffmpeg_command = "ffmpeg.exe"
        
        # 首先检查是否在PATH中
        if shutil.which(ffmpeg_command):
            return True, ""
        
        # 尝试运行ffmpeg命令
        try:
            result = subprocess.run([ffmpeg_command, "-version"], 
                                   stdout=subprocess.PIPE, 
                                   stderr=subprocess.PIPE,
                                   text=True,
                                   shell=True)
            if result.returncode == 0:
                return True, ""
        except:
            pass
        
        # 最后尝试当前目录下的ffmpeg.exe
        if platform.system() == "Windows":
            ffmpeg_exe_path = os.path.join(os.getcwd(), "ffmpeg.exe")
            if os.path.exists(ffmpeg_exe_path):
                try:
                    result = subprocess.run([ffmpeg_exe_path, "-version"], 
                                           stdout=subprocess.PIPE, 
                                           stderr=subprocess.PIPE,
                                           text=True)
                    if result.returncode == 0:
                        return True, ""
                except:
                    pass
        
        return False, "未找到ffmpeg，请确保已安装ffmpeg并添加到系统环境变量中"
    
    def find_sentence_end(self, text, target_position):
        """
        寻找句子结束位置
        
        参数:
            text: 文本内容
            target_position: 目标位置
            
        返回:
            句子结束位置
        """
        if target_position >= len(text):
            return len(text)
        
        # 向后查找句末符号
        for i in range(target_position, min(target_position + 100, len(text))):
            if text[i] in ['。', '？', '！', '\n']:
                return i + 1
        
        # 向前查找句末符号
        for i in range(target_position, max(target_position - 100, 0), -1):
            if text[i] in ['。', '？', '！', '\n']:
                return i + 1
        
        return target_position


# =================== 批处理模块 ===================
class BatchProcessor:
    """批处理器类，处理批量文本到语音的转换"""
    
    def __init__(self, settings):
        """
        初始化批处理器
        
        参数:
            settings: 配音设置字典
        """
        self.settings = settings
        self.text_processor = TextProcessor()
        self.stop_flag = False
        self.cancel_notified = False  # 添加一个标志位，表示是否已经通知用户取消操作
        self.pause_flag = False
        self.current_file_index = -1
        # 添加线程锁和 TTS 引擎实例列表
        self.lock = threading.Lock()
        self.tts_engine_instances = []
        
    def process_file(self, input_file, output_file, progress_callback=None):
        """
        处理单个文件
        
        参数:
            input_file: 输入文件路径
            output_file: 输出文件路径
            progress_callback: 进度回调函数，接收参数(进度百分比, 状态文本)
            
        返回:
            是否成功处理
        """
        try:
            # 更新状态
            if progress_callback:
                progress_callback(0, "读取文件...")
            
            # 读取文本文件
            text = self.text_processor.load_text_file(input_file)
            
            # 处理文本
            processed_text = self.text_processor.process_text(
                text, 
                self.settings["remove_empty_lines"]
            )
            
            # 创建TTS引擎
            tts_engine = TTSEngine(self.settings)
            # 将引擎添加到实例列表中
            with self.lock:
                self.tts_engine_instances.append(tts_engine)
            
            # 更新状态
            if progress_callback:
                progress_callback(5, "准备生成...")
            
            try:
                # 生成音频
                tts_engine.generate_audio(processed_text, output_file, progress_callback)
                return True
            except Exception as e:
                # 判断是否是网络错误
                error_message = str(e)
                network_error_keywords = ["timeout", "connection", "unreachable", "host", 
                                          "network", "refused", "502", "503", "504", "reset", "重试", "retry"]
                
                # 检查错误信息是否包含网络错误关键词
                is_network_error = any(keyword in error_message.lower() for keyword in network_error_keywords)
                
                if is_network_error:
                    print(f"处理文件时遇到网络错误: {error_message}")
                    return False
                else:
                    # 非网络错误，直接抛出异常让上层处理
                    print(f"处理文件时遇到非网络错误: {error_message}")
                    raise e
        
        except Exception as e:
            print(f"处理文件 {input_file} 失败: {str(e)}")
            return False
    
    def process_files_by_batches(self, files, output_dir, progress_callback=None, file_index_callback=None):
        """
        按照批次处理多个文件，符合开发文档中的批量配音流程
        
        参数:
            files: 文件列表，每个文件是一个包含path和name的字典
            output_dir: 输出目录
            progress_callback: 进度回调函数，接收参数(进度百分比, 状态文本)
            file_index_callback: 文件索引回调函数，用于更新当前处理的文件索引
        
        返回:
            处理成功的文件数量
        """
        successful_files = 0
        self.cancel_notified = False  # 重置取消通知标志
        
        for i, file in enumerate(files):
            # 检查是否停止或暂停
            if self.stop_flag:
                break
                
            # 更新当前处理的文件索引
            if file_index_callback:
                file_index_callback(i)
            
            # 更新当前状态
            self.current_file_index = i
            
            # 等待如果处于暂停状态
            while self.pause_flag and not self.stop_flag:
                time.sleep(0.5)
            
            if self.stop_flag:
                break
            
            # 设置输出文件名
            base_name = os.path.splitext(file["name"])[0]
            output_file = os.path.join(output_dir, f"{base_name}.mp3")
            
            # 创建输出目录中的临时文件夹
            temp_dir = os.path.join(output_dir, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 更新状态
            if progress_callback:
                progress_callback(0, f"开始处理文件 {i+1}/{len(files)}: {file['name']}")
            
            # 固定重试策略：仅针对网络错误，最多10次，每次间隔10秒
            max_retries = 10
            retry_interval = 10
            
            # 尝试处理文件，包括重试
            file_success = False
            for retry_count in range(max_retries + 1):
                try:
                    # 如果不是第一次尝试，显示重试信息
                    if retry_count > 0:
                        # 固定等待 10 秒
                        wait_time = retry_interval
                        if progress_callback and not self.cancel_notified:
                            progress_callback(0, f"文件 {i+1}/{len(files)}: 第{retry_count}次重试，等待{wait_time}秒...")
                        
                        print(f"文件 {file['name']} 处理失败，第{retry_count}次重试，等待{wait_time}秒...")
                        time.sleep(wait_time)
                    
                    # 检查是否停止
                    if self.stop_flag:
                        break
                    
                    # 定义文件进度回调函数（避免None调用）
                    def file_progress_callback(p, s):
                        if progress_callback and not self.cancel_notified:
                            # 直接转发底层状态文本，确保显示批次信息
                            progress_callback(p, f"文件 {i+1}/{len(files)}: {s}")
                    
                    # 处理文件
                    success = self.process_file(
                        file["path"], 
                        output_file,
                        file_progress_callback
                    )
                    
                    if success:
                        file_success = True
                        successful_files += 1
                        
                        if progress_callback and not self.cancel_notified:
                            progress_callback(100, f"文件 {i+1}/{len(files)} 处理完成")
                        
                        # 成功处理，跳出重试循环
                        break
                    elif retry_count >= max_retries:
                        # 所有重试都失败
                        if progress_callback and not self.cancel_notified:
                            progress_callback(0, f"文件 {i+1}/{len(files)} 处理失败：已达到最大重试次数{max_retries}")
                        print(f"处理文件 {file['name']} 失败: 已达到最大重试次数{max_retries}")
                    else:
                        # 本次尝试失败，但还可以继续重试
                        continue
                
                except Exception as e:
                    error_message = str(e)
                    print(f"处理文件 {file['name']} 异常: {error_message}")
                    
                    # 判断是否是网络错误
                    network_error_keywords = ["timeout", "connection", "unreachable", "host", 
                                          "network", "refused", "502", "503", "504", "reset", "重试", "retry"]
                    is_network_error = any(keyword in error_message.lower() for keyword in network_error_keywords)
                    
                    # 如果是网络错误且未达到最大重试次数，则继续重试
                    if is_network_error and retry_count < max_retries:
                        # 固定等待 10 秒
                        wait_time = retry_interval
                        if progress_callback and not self.cancel_notified:
                            progress_callback(0, f"文件 {i+1}/{len(files)}网络错误，第{retry_count+1}次重试，等待{wait_time}秒...")
                        
                        print(f"文件 {file['name']} 遇到网络错误: {error_message}，第{retry_count+1}次重试，等待{wait_time}秒...")
                        time.sleep(wait_time)
                        continue
                    else:
                        # 非网络错误或已达到最大重试次数
                        if progress_callback and not self.cancel_notified:
                            progress_callback(0, f"文件 {i+1}/{len(files)} 处理失败: {error_message}")
                        break
            
            # 若本文件经过固定次数仍未成功，弹窗询问是否继续重试
            while not file_success:
                try:
                    continue_retry = messagebox.askyesno(
                        "重试确认",
                        f"文件 {file['name']} 已连续重试{max_retries}次仍失败，是否继续重试？"
                    )
                except Exception:
                    # 在非GUI环境下安全降级
                    continue_retry = False

                if not continue_retry:
                    # 用户选择不继续重试，跳出循环，进入下一个文件
                    break

                # 用户选择继续，则再次按照同样策略尝试 max_retries 次
                for extra_retry in range(1, max_retries + 1):
                    if self.stop_flag:
                        break

                    print(f"文件 {file['name']} 用户确认后额外重试 {extra_retry}/{max_retries}")

                    if progress_callback and not self.cancel_notified:
                        progress_callback(0, f"文件 {i+1}/{len(files)}: 继续重试 {extra_retry}/{max_retries}...")

                    success = self.process_file(file["path"], output_file, None)

                    if success:
                        file_success = True
                        successful_files += 1

                        if progress_callback and not self.cancel_notified:
                            progress_callback(100, f"文件 {i+1}/{len(files)} 处理完成")
                        break

                    # 若仍然失败，等待固定间隔再试
                    time.sleep(retry_interval)

                # 若通过额外重试已成功，跳出 while 循环
                if file_success:
                    break
        
        return successful_files
    
    def split_text_into_batches(self, text, batch_size=10000):
        """
        将文本按行拆分成多个批次，保证每个批次不超过指定字数
        
        参数:
            text: 要拆分的文本
            batch_size: 每个批次的最大字符数（默认5000字）
            
        返回:
            批次列表，每个批次为一段文本
        """
        # 按行分割文本
        lines = text.splitlines()
        
        # 初始化批次数组
        batches = []
        current_batch = []
        current_size = 0
        
        # 逐行处理
        for line in lines:
            line_length = len(line)
            
            # 如果加上当前行会超出批次大小，且当前批次不为空，则创建新批次
            if current_size + line_length > batch_size and current_batch:
                batches.append("\n".join(current_batch))
                current_batch = [line]
                current_size = line_length
            else:
                # 否则，将行添加到当前批次
                current_batch.append(line)
                current_size += line_length
        
        # 添加最后一个批次（如果有）
        if current_batch:
            batches.append("\n".join(current_batch))
        
        return batches
    
    def process_text_in_batches(self, text, output_file, progress_callback=None):
        """
        按批次处理文本，提高稳定性并支持中断和恢复
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
            progress_callback: 进度回调函数
            
        返回:
            是否成功处理
        """
        try:
            # 更新状态
            if progress_callback and not self.cancel_notified:
                progress_callback(0, "正在准备批处理...")
            
            # 分割文本
            batch_size = self.settings.get("batch_size", 10000)
            batches = self.split_text_into_batches(text, batch_size)
                
            # 如果只有一个批次，直接处理
            if len(batches) == 1:
                # 创建TTS引擎
                tts_engine = TTSEngine(self.settings)
                with self.lock:
                    self.tts_engine_instances.append(tts_engine)
                
                try:
                    if progress_callback and not self.cancel_notified:
                        progress_callback(5, "准备生成...")
                    
                    # 直接生成
                    result = tts_engine.generate_audio(batches[0], output_file, progress_callback)
                    return result
                except Exception as e:
                    print(f"生成音频失败: {str(e)}")
                    return False
                finally:
                    # 从引擎列表中移除
                    with self.lock:
                        if tts_engine in self.tts_engine_instances:
                            self.tts_engine_instances.remove(tts_engine)
            
            # 多个批次，需要分别处理然后合并
            if progress_callback and not self.cancel_notified:
                progress_callback(5, f"将分为{len(batches)}个批次处理...")
                
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(output_file), "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 批次文件路径列表
            batch_files = []
            
            # 记录失败的批次
            failed_batches = []
            
            # 获取CPU核心数，决定并行数量
            cpu_count = multiprocessing.cpu_count()
            # 使用核心数的一半+1作为并行度，但不超过总批次数
            parallel_count = min(len(batches), max(2, cpu_count // 2 + 1))
            
            # 创建TTS引擎实例
            tts_engine = TTSEngine(self.settings)
            with self.lock:
                self.tts_engine_instances.append(tts_engine)
            
            # 将批次均匀分配给各个线程
            batch_distribution = []
            for i in range(parallel_count):
                batch_distribution.append([])
            
            for i, batch in enumerate(batches):
                thread_index = i % parallel_count
                batch_distribution[thread_index].append((i, batch))
            
            # 创建进度管理器
            from tqdm import tqdm
            
            # 使用线程处理每个批次
            import threading
            
            # 创建线程锁，用于同步cancel_notified标志的访问
            self.lock = threading.Lock()
            
            # 定义批次处理函数
            def process_batches_thread(thread_index, batches_to_process, results):
                # 创建该线程使用的TTS引擎
                thread_engine = TTSEngine(self.settings)
                with self.lock:
                    self.tts_engine_instances.append(thread_engine)
                
                try:
                    with tqdm(total=len(batches_to_process), desc=f"Thread-{thread_index}") as pbar:
                        for batch_index, batch_text in batches_to_process:
                            if self.stop_flag:
                                break
                                
                            # 定义批次进度回调函数
                            def batch_progress_callback(p, s):
                                # 更新进度条
                                pbar.set_description(f"Thread-{thread_index} Batch-{batch_index+1} {s}")
                                # 检查是否应该发送进度更新
                                with self.lock:
                                    should_update = not self.cancel_notified
                                
                                # 计算总体进度
                                if should_update:
                                    self._update_batch_progress(
                                        progress_callback, 
                                        0, 
                                        batch_index, 
                                        len(batches), 
                                        p, s, 
                                        90 / len(batches)
                                    )
                            
                            # 生成临时文件路径
                            temp_file = os.path.join(temp_dir, f"batch_{batch_index+1}.mp3")
                            batch_files.append(temp_file)
                            
                            # 处理批次
                            success = self._process_batch(
                                thread_engine, 
                                batch_text, 
                                temp_file, 
                                batch_progress_callback
                            )
                            
                            # 更新结果
                            results[batch_index] = {
                                "success": success,
                                "file": temp_file if success else None
                            }
                            
                            # 更新进度条
                            pbar.update(1)
                            
                            # 检查是否需要停止
                            if self.stop_flag:
                                break
                finally:
                    # 确保总是从实例列表中移除该引擎
                    with self.lock:
                        try:
                            if thread_engine in self.tts_engine_instances:
                                self.tts_engine_instances.remove(thread_engine)
                            # 如果在停止状态，主动停止引擎
                            if self.stop_flag:
                                thread_engine.stop()
                        except:
                            pass
            
            # 创建结果数组
            results = [None] * len(batches)
            
            # 创建线程
            threads = []
            for i, batch_group in enumerate(batch_distribution):
                if batch_group:  # 只为非空批次组创建线程
                    thread = threading.Thread(
                        target=process_batches_thread, 
                        args=(i, batch_group, results)
                    )
                    threads.append(thread)
                    thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
                
            # 检查是否被停止
            if self.stop_flag:
                with self.lock:
                    if not self.cancel_notified and progress_callback:
                        progress_callback(0, "操作已取消")
                        self.cancel_notified = True
                print("批量处理已取消")
                return False
            
            # 检查结果
            for i, result in enumerate(results):
                if not result or not result["success"]:
                    failed_batches.append(i)
            
            # 更新状态
            if progress_callback and not self.cancel_notified:
                if failed_batches:
                    status_text = f"{len(failed_batches)}/{len(batches)}个批次失败，继续合并可用部分..."
                    progress_callback(80, status_text)
                else:
                    progress_callback(80, "所有批次处理成功，正在合并...")
            
            # 过滤出成功的批次文件
            valid_batch_files = []
            for result in results:
                if result and result["success"] and result["file"]:
                    valid_batch_files.append(result["file"])
            
            # 没有有效的批次文件
            if not valid_batch_files:
                if progress_callback and not self.cancel_notified:
                    progress_callback(0, "所有批次处理失败")
                return False
            
            # 检查是否被停止
            if self.stop_flag:
                with self.lock:
                    if not self.cancel_notified and progress_callback:
                        progress_callback(0, "操作已取消")
                        self.cancel_notified = True
                print("批量处理已取消")
                return False
                
            # 合并所有批次的音频文件
            asyncio.run(tts_engine._merge_audio_files(output_file, valid_batch_files))
            
            if progress_callback and not self.cancel_notified:
                progress_callback(100, "处理完成" + (f"（{len(failed_batches)}/{len(batches)}个批次失败）" if failed_batches else ""))
            
            return True
            
        except Exception as e:
            print(f"批量处理文本失败: {str(e)}")
            return False
            
    def _process_batch(self, tts_engine, text, output_file, progress_callback=None):
        """
        处理单个批次
        
        参数:
            tts_engine: TTS引擎实例
            text: 批次文本
            output_file: 输出文件路径
            progress_callback: 进度回调函数
            
        返回:
            bool: 是否成功处理
        """
        try:
            # 检查是否要停止
            if self.stop_flag:
                print("批次处理已取消")
                return False
                
            # 固定重试策略：仅针对网络错误，最多10次，每次间隔10秒
            max_retries = 10
            retry_interval = 10
            
            # 保存最后一个异常
            last_exception = None
            
            # 进行多次重试
            for retry_count in range(max_retries + 1):
                try:
                    # 检查是否要停止
                    if self.stop_flag:
                        print("批次重试已取消")
                        return False
                        
                    # 更新状态
                    if progress_callback and retry_count > 0:
                        progress_callback(0, f"第{retry_count}次重试中...")
                    
                    # 生成音频
                    if progress_callback:
                        tts_engine.generate_audio(text, output_file, progress_callback)
                    else:
                        tts_engine.generate_audio(text, output_file)
                    return True  # 成功完成，返回成功状态
                    
                except Exception as e:
                    # 检查是否要停止
                    if self.stop_flag:
                        print("批次处理失败后已取消")
                        return False
                        
                    # 保存最后一次异常
                    last_exception = e
                    error_msg = str(e).lower()
                    
                    # 判断是否是网络错误
                    network_error_keywords = ["timeout", "connection", "unreachable", "host", 
                                           "network", "refused", "502", "503", "504", "reset",
                                           "invalid response", "重试", "retry", "socket"]
                    
                    # 如果不是网络错误或已达到最大重试次数，则跳出重试循环
                    is_network_error = any(keyword in error_msg for keyword in network_error_keywords)
                    if not is_network_error or retry_count >= max_retries:
                        break
                        
                    # 固定等待 10 秒
                    wait_time = retry_interval
                    print(f"批次处理遇到网络错误: {error_msg}, {wait_time}秒后重试 ({retry_count+1}/{max_retries})")
                    
                    # 更新状态
                    if progress_callback:
                        progress_callback(0, f"网络错误，{wait_time}秒后重试...")
                        
                    # 等待一段时间后重试
                    time.sleep(wait_time)
            
            # 如果所有重试都失败，记录错误并返回失败状态
            if last_exception:
                print(f"处理批次失败: 所有重试都失败")
                if progress_callback:
                    progress_callback(0, "处理失败: 所有重试都失败")
                return False
                
        except Exception as e:
            # 检查是否要停止
            if self.stop_flag:
                print("批次异常处理已取消")
                return False
                
            print(f"批次处理失败: {str(e)}")
            if progress_callback:
                progress_callback(0, f"处理失败: {str(e)}")
            return False
        
        return False  # 默认失败状态，确保函数有返回值
    
    def _update_batch_progress(self, progress_callback, start_batch, current_batch, total_batches, 
                              batch_progress, batch_status, batch_weight):
        """
        更新批次处理进度
        
        参数:
            progress_callback: 进度回调函数
            start_batch: 当前批次的起始索引
            current_batch: 当前处理的批次索引
            total_batches: 总批次数
            batch_progress: 当前批次的进度百分比
            batch_status: 当前批次的状态文本
            batch_weight: 每个批次的进度权重
        """
        if progress_callback and not self.cancel_notified:
            # 计算总体进度
            batch_offset = current_batch - start_batch
            batch_base_progress = 5 + (current_batch / total_batches) * 90
            within_batch_progress = (batch_progress / 100) * (batch_weight)
            total_progress = int(batch_base_progress + within_batch_progress)  # 确保是整数
            
            # 更新进度和状态
            progress_callback(
                total_progress,
                f"批次 {current_batch+1}/{total_batches}: {batch_status}"
            )
    
    def stop(self):
        """停止处理"""
        print("批处理器: 正在执行停止操作...")
        self.stop_flag = True
        
        # 设置取消已通知标志，防止多次弹窗
        with self.lock if hasattr(self, 'lock') else threading.Lock():
            self.cancel_notified = True
        
        # 这里需要尝试停止任何正在运行的 TTSEngine 实例
        if hasattr(self, 'tts_engine_instances') and self.tts_engine_instances:
            print(f"批处理器: 尝试停止 {len(self.tts_engine_instances)} 个TTS引擎实例...")
            active_engines = list(self.tts_engine_instances) # Iterate over a copy
            self.tts_engine_instances.clear() # 清空列表，防止在停止期间有新的添加

            for engine in active_engines:
                if engine:
                    try:
                        print(f"批处理器: 正在停止 TTS 引擎 {id(engine)}...")
                        engine.stop() # 假设这个方法会处理好内部子进程的停止
                    except Exception as e:
                        print(f"停止 TTS 引擎 {id(engine)} 时出错: {str(e)}")
                        
        # 在所有引擎都收到停止信号后，增加一个延时
        # 这给底层进程（如ffmpeg）一些时间来释放文件句柄
        print("批处理器: 所有TTS引擎已通知停止，等待文件句柄释放...")
        time.sleep(2) # 尝试等待2秒

        print("批处理器: 停止操作完成。")
    
    def pause(self):
        """暂停处理"""
        self.pause_flag = True
    
    def resume(self):
        """继续处理"""
        self.pause_flag = False


# =================== TTS引擎核心模块 ===================
"""
TTS引擎核心模块，负责与各种语音合成引擎交互
"""


class TTSEngine:
    """TTS引擎类，处理文本到语音的转换"""
    
    def __init__(self, settings):
        """
        初始化TTS引擎
        
        参数:
            settings: 配音设置字典
        """
        self.settings = settings
        self.temp_files = []
        self.stop_flag = False
        # 新增：批量任务列表
        self._batch_tasks = []  # 保存所有批量处理的asyncio.Task对象
        # 创建事件循环
        self.loop = asyncio.new_event_loop()
        
        # 初始化子进程信息
        self.startupinfo = None
        if os.name == 'nt':  # 如果是Windows系统
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            self.startupinfo.wShowWindow = subprocess.SW_HIDE
            # 设置进程创建标志，完全分离控制台
            self.creation_flags = subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
        else:
            self.creation_flags = 0
    
    def _preprocess_text(self, text):
        """
        特殊读法预处理，严格按需求处理：
        1. 区间/带单位的-（如 10-20、50公斤-60公斤）读作"至"
        2. 数字前的-（如 -10）读作"减"
        3. 非数字/非ASCII后接-数字（如 伤害-10）读作"减"
        4. 其他所有-都读作"至"
        其他符号（+、/、*、%）按原逻辑处理
        """
        # 先处理百分号：数字% -> 百分之数字
        text = re.sub(r'(\d+)%', r'百分之\1', text)
        # 处理加号
        text = text.replace('+', '加')
        # 处理除号
        text = text.replace('/', '比')
        # 处理乘号
        text = text.replace('*', '乘')
        # 1. 区间/带单位的-（如 10-20、50公斤-60公斤）
        text = re.sub(r'(\d+)\s*(?:[公斤克千米吨厘寸尺毫微个份包片元角分度]*)-((?:\s*)[0-9]+)', r'\1至\2', text)
        # 2. 数字前的-（如 -10）
        text = re.sub(r'(^|\s+)-(\d)', r'\1减\2', text)
        # 3. 非数字/非ASCII后接-数字（如 伤害-10）
        text = re.sub(r'([^\u0000-\u007F\d])-(\d)', r'\1减\2', text)
        # 4. 其他所有-都读作"至"
        text = text.replace('-', '至')
        return text
    
    def generate_preview(self, text, output_file):
        """
        生成预览音频文件
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 预处理文本用于语音合成，字幕保留原文
            tts_text = self._preprocess_text(text)
            # 运行异步代码
            self.loop.run_until_complete(self._generate_preview_async(tts_text, output_file))
        except Exception as e:
            print(f"生成预览失败: {str(e)}")
            raise e
    
    async def _generate_preview_async(self, text, output_file):
        """
        异步生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 仅支持 Edge TTS，引擎字段若非 Edge 自动回退
        engine = self.settings.get("engine", "edge-tts")
        if engine != "edge-tts":
            engine = "edge-tts"
            self.settings["engine"] = "edge-tts"
        
        await self._edge_tts_preview(text, output_file)
        # 创建字幕文件
        raw_txt_file = os.path.splitext(output_file)[0] + ".raw.txt"
        with open(raw_txt_file, 'w', encoding='utf-8') as f:
            f.write(text + '\n')
        srt_file = os.path.splitext(output_file)[0] + ".srt"
    
    async def _edge_tts_preview(self, text, output_file):
        """
        使用Edge TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 从设置中获取参数
            voice = self.settings["voice"]
            # 修复计算逻辑，确保rate和volume使用正确的格式
            speed = self.settings['speed']
            rate = f"+{int((speed - 1) * 100)}%" if speed > 1 else f"-{int(abs((speed - 1) * 100))}%"
            if speed == 1:
                rate = "+0%"
            volume = f"+{int((self.settings['volume'] - 1) * 100)}%" if self.settings['volume'] > 1 else f"-{int(abs((self.settings['volume'] - 1) * 100))}%"
            if self.settings['volume'] == 1:
                volume = "+0%"
            
            # 修复pitch参数格式，使用Hz单位
            pitch_value = int((self.settings['pitch'] - 1) * 50)  # 将0.5-2.0的值映射到-25Hz到+50Hz
            pitch = f"+{pitch_value}Hz" if pitch_value >= 0 else f"{pitch_value}Hz"
            if self.settings['pitch'] == 1:
                pitch = "+0Hz"
            
            # 使用Edge TTS生成音频
            communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume, pitch=pitch)
            await communicate.save(output_file)
        except Exception as e:
            print(f"Edge TTS预览失败: {str(e)}")
            raise e
    
    async def _azure_tts_preview(self, text, output_file):
        """
        使用Azure TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API密钥是否已设置
            api_key = self.settings.get("azure_key", "")
            region = self.settings.get("azure_region", "")
            
            if not api_key or not region:
                raise Exception("Azure TTS API密钥或区域未设置，请在设置中配置")
            
            # 从设置中获取参数
            voice = self.settings["voice"]
            speed = self.settings['speed']
            pitch = self.settings['pitch']
            volume = self.settings['volume']
            
            # 构建SSML
            ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
                <voice name="{voice}">
                    <prosody rate="{speed}" pitch="{int((pitch-1)*50)}Hz" volume="{int((volume-1)*100)}%">
                        {text}
                    </prosody>
                </voice>
            </speak>
            """
            
            # 设置API请求头
            headers = {
                "Ocp-Apim-Subscription-Key": api_key,
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3",
                "User-Agent": "TTS-Tool"
            }
            
            # 设置API请求URL
            url = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"
            
            # 发送请求并获取音频数据
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, data=ssml.encode('utf-8')) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Azure TTS API错误: {response.status}, {error_text}")
                    
                    # 将音频数据写入文件
                    with open(output_file, "wb") as f:
                        f.write(await response.read())
            
        except Exception as e:
            print(f"Azure TTS预览失败: {str(e)}")
            raise e
    
    async def _baidu_tts_preview(self, text, output_file):
        """
        使用百度TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            app_id = self.settings.get("baidu_app_id", "")
            api_key = self.settings.get("baidu_api_key", "")
            secret_key = self.settings.get("baidu_secret_key", "")
            
            if not app_id or not api_key or not secret_key:
                raise Exception("百度TTS API信息未设置，请在设置中配置")
            
            # 从设置中获取参数
            speed = int(self.settings['speed'] * 5)  # 百度TTS语速范围0-9
            pitch = int(self.settings['pitch'] * 9)  # 百度TTS音调范围0-9
            volume = int(self.settings['volume'] * 15)  # 百度TTS音量范围0-15
            
            # 获取访问令牌的URL
            token_url = f"https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={api_key}&client_secret={secret_key}"
            
            # 获取访问令牌
            async with aiohttp.ClientSession() as session:
                async with session.post(token_url) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"获取百度访问令牌失败: {response.status}, {error_text}")
                    
                    result = await response.json()
                    access_token = result.get("access_token")
                    
                    if not access_token:
                        raise Exception("获取百度访问令牌失败")
                    
                    # 构建TTS API请求参数
                    tts_url = "https://tsn.baidu.com/text2audio"
                    params = {
                        "tex": text,
                        "tok": access_token,
                        "cuid": app_id,
                        "ctp": 1,
                        "lan": "zh",
                        "spd": speed,
                        "pit": pitch,
                        "vol": volume,
                        "per": 0,  # 发音人，可根据需要修改
                        "aue": 3,  # mp3格式
                    }
                    
                    # 发送TTS请求
                    async with session.post(tts_url, data=params) as tts_response:
                        content_type = tts_response.headers.get('Content-Type')
                        
                        if content_type == 'application/json':
                            # 错误响应
                            error_json = await tts_response.json()
                            raise Exception(f"百度TTS合成失败: {error_json}")
                        
                        # 将音频数据写入文件
                        with open(output_file, "wb") as f:
                            f.write(await tts_response.read())
            
        except Exception as e:
            print(f"百度TTS预览失败: {str(e)}")
            raise e
    
    async def _xunfei_tts_preview(self, text, output_file):
        """
        使用讯飞TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            app_id = self.settings.get("xunfei_app_id", "")
            api_key = self.settings.get("xunfei_api_key", "")
            api_secret = self.settings.get("xunfei_api_secret", "")
            
            if not app_id or not api_key or not api_secret:
                raise Exception("讯飞TTS API信息未设置，请在设置中配置")
            
            # 由于讯飞TTS API的复杂性，这里只提供示例代码
            # 讯飞TTS需要使用WebSocket进行通信，需要更复杂的实现
            # 这里使用临时方案，通过subprocess调用外部脚本或使用SDK
            
            # 创建临时目录存放配置文件
            temp_dir = tempfile.mkdtemp()
            config_file = os.path.join(temp_dir, "xunfei_config.json")
            
            # 创建配置文件
            config = {
                "app_id": app_id,
                "api_key": api_key,
                "api_secret": api_secret,
                "text": text,
                "speed": self.settings['speed'],
                "pitch": self.settings['pitch'],
                "volume": self.settings['volume']
            }
            
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False)
            
            # 这里需要一个外部脚本或SDK来实际调用讯飞TTS API
            # 这里只是示例，实际应用需要根据讯飞TTS API的文档进行实现
            raise Exception("讯飞TTS功能尚未完全实现，请等待后续更新")
            
        except Exception as e:
            print(f"讯飞TTS预览失败: {str(e)}")
            raise e
    
    async def _tencent_tts_preview(self, text, output_file):
        """
        使用腾讯TTS生成预览音频
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        try:
            # 检查API信息是否已设置
            secret_id = self.settings.get("tencent_secret_id", "")
            secret_key = self.settings.get("tencent_secret_key", "")
            
            if not secret_id or not secret_key:
                raise Exception("腾讯TTS API信息未设置，请在设置中配置")
            
            # 由于腾讯TTS API的复杂性，这里只提供示例代码
            # 腾讯TTS需要使用签名等安全机制，需要更复杂的实现
            # 这里使用临时方案，通过subprocess调用外部脚本或使用SDK
            
            # 创建临时目录存放配置文件
            temp_dir = tempfile.mkdtemp()
            config_file = os.path.join(temp_dir, "tencent_config.json")
            
            # 创建配置文件
            config = {
                "secret_id": secret_id,
                "secret_key": secret_key,
                "text": text,
                "speed": self.settings['speed'],
                "pitch": self.settings['pitch'],
                "volume": self.settings['volume']
            }
            
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False)
            
            # 这里需要一个外部脚本或SDK来实际调用腾讯TTS API
            # 这里只是示例，实际应用需要根据腾讯TTS API的文档进行实现
            raise Exception("腾讯TTS功能尚未完全实现，请等待后续更新")
            
        except Exception as e:
            print(f"腾讯TTS预览失败: {str(e)}")
            raise e
    
    def generate_audio(self, text, output_file, progress_callback=None):
        """
        生成完整音频文件
        参数:
            text: 文本内容
            output_file: 输出文件路径
            progress_callback: 进度回调函数，接收参数(进度百分比, 状态文本)
        """
        try:
            # 重置停止标志
            self.stop_flag = False
            # 预处理文本用于语音合成，字幕保留原文
            tts_text = self._preprocess_text(text)
            # 运行异步代码
            self.loop.run_until_complete(
                self._generate_audio_async(tts_text, output_file, progress_callback)
            )
        except Exception as e:
            print(f"生成音频失败: {str(e)}")
            # 清理临时文件
            self._cleanup_temp_files()
            raise e
    
    async def _generate_audio_async(self, text, output_file, progress_callback=None):
        """
        异步生成完整音频
        """
        try:
            # 准备输出目录
            output_dir = os.path.dirname(output_file)
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建临时目录
            temp_dir = os.path.join(output_dir, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 生成字幕文件路径
            srt_file = os.path.splitext(output_file)[0] + ".srt"
            # 新增：保存原始文本分行到 .raw.txt
            raw_txt_file = os.path.splitext(output_file)[0] + ".raw.txt"
            
            # 保存原始文本，包含行分隔
            original_lines = text.splitlines()
            with open(raw_txt_file, 'w', encoding='utf-8') as f:
                for line in original_lines:
                    f.write(line + '\n')
            
            # 更新进度
            if progress_callback:
                progress_callback(5, "分析文本...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            
            # 按行分割文本
            lines = text.splitlines()
            
            # 保存原始行与行号的映射，用于后续字幕匹配
            self.original_text_mapping = {i: line for i, line in enumerate(lines) if line.strip()}
            
            # 计算批次大小
            batch_size = self.settings["batch_size"]
            line_batches = []
            current_batch = []
            current_size = 0
            
            # 保存每个批次中包含的原始行索引，用于后续字幕匹配
            batch_line_indices = []
            current_indices = []
            
            for i, line in enumerate(lines):
                line_len = len(line)
                if current_size + line_len > batch_size and current_batch:
                    line_batches.append("\n".join(current_batch))
                    batch_line_indices.append(current_indices)
                    current_batch = [line]
                    current_indices = [i]
                    current_size = line_len
                else:
                    current_batch.append(line)
                    current_indices.append(i)
                    current_size += line_len
                    
            # 添加最后一个批次
            if current_batch:
                line_batches.append("\n".join(current_batch))
                batch_line_indices.append(current_indices)
            
            # 更新进度
            if progress_callback:
                progress_callback(10, f"准备处理 {len(line_batches)} 个批次...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            
            # 生成每个批次的临时文件
            temp_audio_files = []
            temp_srt_files = []
            # 设置并发数
            concurrent_tasks = min(self.settings.get("concurrent_tasks", 4), len(line_batches))
            semaphore = asyncio.Semaphore(concurrent_tasks)
            print(f"使用并行任务数：{concurrent_tasks}，批次总数：{len(line_batches)}")
            tasks = []
            self._batch_tasks = []  # 重置批量任务列表
            # 初始化已完成批次数和当前进度
            self._completed_batches = 0
            self._current_progress = 0  # 0-10% 用于批次开始动态显示
            for i, batch_text in enumerate(line_batches):
                temp_file = os.path.join(temp_dir, f"batch_{i+1}.mp3")
                temp_srt = os.path.join(temp_dir, f"batch_{i+1}.srt")
                temp_audio_files.append(temp_file)
                temp_srt_files.append(temp_srt)
                self.temp_files.append(temp_srt)  # 确保批次字幕也加入临时文件列表
                # 用asyncio.create_task创建任务，便于后续取消
                task = asyncio.create_task(self._process_batch(
                    i,
                    batch_text,
                    temp_file,
                    temp_srt,
                    semaphore,
                    len(line_batches),
                    progress_callback,
                    batch_line_indices[i]  # 传递当前批次的原始行索引
                ))
                tasks.append(task)
                self._batch_tasks.append(task)
            # 检查是否被停止
            if self.stop_flag:
                for task in self._batch_tasks:
                    if not task.done():
                        task.cancel()
                if progress_callback:
                    progress_callback(0, "操作已取消")
                raise Exception("操作已取消")
            # 并发执行所有批次
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
            except asyncio.CancelledError:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            # 检查是否有任务失败
            failed_tasks = [i for i, result in enumerate(results) if isinstance(result, Exception)]
            if failed_tasks:
                failed_indices = ", ".join([str(i+1) for i in failed_tasks])
                raise Exception(f"以下批次处理失败: {failed_indices}")
            
            # 更新进度
            if progress_callback:
                progress_callback(80, "合并音频...")
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            
            # 合并所有临时文件
            await self._merge_audio_files(output_file, temp_audio_files)
            
            # 检查是否被停止
            if self.stop_flag:
                if progress_callback:
                    progress_callback(0, "操作已取消")
                return
            
            # 更新进度
            if progress_callback:
                progress_callback(90, "合并字幕...")
            
            # 合并字幕文件
            await self._merge_subtitle_files(srt_file, temp_srt_files, temp_audio_files, progress_callback)
            
            # 更新进度
            if progress_callback:
                progress_callback(100, "处理完成")
            
        except asyncio.CancelledError:
            if progress_callback:
                progress_callback(0, "操作已取消")
            return
        except Exception as e:
            print(f"生成音频失败: {str(e)}")
            raise e
        finally:
            # 清理未完成的临时文件（保留已完成的）
            self._cleanup_temp_files()
            self._batch_tasks = []  # 清空任务列表
    
    async def _merge_subtitle_files(self, output_srt, srt_files, audio_files, progress_callback=None):
        """
        合并字幕文件，并根据音频文件长度调整时间戳
        
        参数:
            output_srt: 输出字幕文件路径
            srt_files: 要合并的字幕文件列表
            audio_files: 对应的音频文件列表（用于获取长度信息）
        """
        try:
            if not srt_files:
                return
            
            # 创建最终字幕文件
            final_subs = pysrt.SubRipFile()
            
            # 字幕索引计数器
            subtitle_index = 1
            
            # 当前偏移时间（秒）
            current_offset = 0.0
            
            total_parts = len(srt_files)
            # 处理每个字幕文件
            for i, (srt_file, audio_file) in enumerate(zip(srt_files, audio_files)):
                print(f"处理第 {i+1}/{len(srt_files)} 个字幕文件")
                
                # 跳过不存在的文件
                if not os.path.exists(srt_file):
                    print(f"字幕文件不存在，跳过: {srt_file}")
                    continue
                    
                if not os.path.exists(audio_file):
                    print(f"音频文件不存在，跳过: {audio_file}")
                    continue
                
                # 获取音频文件精确时长
                try:
                    # 使用FFmpeg获取音频时长（秒）
                    command = [
                        "ffprobe",
                        "-v", "error",
                        "-show_entries", "format=duration",
                        "-of", "default=noprint_wrappers=1:nokey=1",
                        audio_file
                    ]
                    
                    # 创建进程
                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                    
                    # 等待进程完成
                    stdout, stderr = await process.communicate()
                    
                    # 检查是否成功
                    if process.returncode != 0:
                        print(f"获取音频时长失败: {stderr.decode()}")
                        continue
                    
                    # 解析输出获取时长
                    audio_duration = float(stdout.decode().strip())
                    
                    if audio_duration <= 0:
                        print(f"警告: 音频时长无效 ({audio_duration}秒)")
                        continue
                        
                    print(f"音频文件时长: {audio_duration:.3f}秒")
                
                except Exception as e:
                    print(f"获取音频时长出错: {str(e)}")
                    continue
                
                # 读取字幕文件
                try:
                    current_subs = pysrt.open(srt_file, encoding='utf-8')
                    print(f"成功读取字幕文件，包含 {len(current_subs)} 条字幕")
                except Exception as e:
                    print(f"读取字幕文件失败: {str(e)}")
                    continue
                
                # 如果字幕文件为空，尝试基于音频和原始文本生成字幕
                if len(current_subs) == 0:
                    print(f"字幕文件为空，尝试基于音频和原始文本生成字幕")
                    
                    # 这里可以添加逻辑来基于音频和原始文本生成字幕
                    # 现在暂时跳过
                    continue
                
                # 将当前字幕添加到最终字幕列表，调整时间戳
                for sub in current_subs:
                    # 创建新字幕
                    new_sub = pysrt.SubRipItem()
                    new_sub.index = subtitle_index
                    subtitle_index += 1
                    
                    # 复制文本
                    new_sub.text = sub.text
                    
                    # 计算原始时间（秒）
                    orig_start_sec = sub.start.hours * 3600 + sub.start.minutes * 60 + sub.start.seconds + sub.start.milliseconds / 1000.0
                    orig_end_sec = sub.end.hours * 3600 + sub.end.minutes * 60 + sub.end.seconds + sub.end.milliseconds / 1000.0
                    
                    # 调整时间戳比例，确保适配音频文件实际时长
                    # 计算字幕文件中的最大时间戳
                    max_subtitle_time = 0
                    for s in current_subs:
                        end_time = s.end.hours * 3600 + s.end.minutes * 60 + s.end.seconds + s.end.milliseconds / 1000.0
                        max_subtitle_time = max(max_subtitle_time, end_time)
                    
                    # 如果字幕总时长与音频时长有显著差异，调整比例
                    if max_subtitle_time > 0 and abs(max_subtitle_time - audio_duration) > 0.5:
                        time_scale = audio_duration / max_subtitle_time
                        orig_start_sec *= time_scale
                        orig_end_sec *= time_scale
                        print(f"调整字幕时间比例: {time_scale:.3f}")
                    
                    # 添加当前偏移
                    new_start_sec = orig_start_sec + current_offset
                    new_end_sec = orig_end_sec + current_offset
                    
                    # 设置新的开始时间
                    new_sub.start.hours = int(new_start_sec // 3600)
                    new_sub.start.minutes = int((new_start_sec % 3600) // 60)
                    new_sub.start.seconds = int(new_start_sec % 60)
                    new_sub.start.milliseconds = int((new_start_sec - int(new_start_sec)) * 1000)
                    
                    # 设置新的结束时间
                    new_sub.end.hours = int(new_end_sec // 3600)
                    new_sub.end.minutes = int((new_end_sec % 3600) // 60)
                    new_sub.end.seconds = int(new_end_sec % 60)
                    new_sub.end.milliseconds = int((new_end_sec - int(new_end_sec)) * 1000)
                    
                    # 添加到最终字幕列表
                    final_subs.append(new_sub)
                
                # 更新时间偏移，使用音频的实际时长
                current_offset += audio_duration
                print(f"当前总偏移: {current_offset:.3f}秒")
            
            # 合并后的字幕后处理 - 修复重叠问题和确保字幕不会太短
            if len(final_subs) > 1:
                print("执行字幕后处理，修复可能的重叠问题...")
                for i in range(1, len(final_subs)):
                    prev_sub = final_subs[i-1]
                    curr_sub = final_subs[i]
                    
                    # 获取前一个字幕的结束时间和当前字幕的开始时间（秒）
                    prev_end_sec = prev_sub.end.hours * 3600 + prev_sub.end.minutes * 60 + prev_sub.end.seconds + prev_sub.end.milliseconds / 1000.0
                    curr_start_sec = curr_sub.start.hours * 3600 + curr_sub.start.minutes * 60 + curr_sub.start.seconds + curr_sub.start.milliseconds / 1000.0
                    
                    # 如果存在重叠，调整前一个字幕的结束时间
                    if prev_end_sec > curr_start_sec:
                        # 设置一个小的间隔（0.05秒）
                        gap = 0.05
                        new_prev_end = curr_start_sec - gap
                        
                        # 确保字幕不会太短（至少0.3秒）
                        prev_start_sec = prev_sub.start.hours * 3600 + prev_sub.start.minutes * 60 + prev_sub.start.seconds + prev_sub.start.milliseconds / 1000.0
                        if new_prev_end - prev_start_sec < 0.3:
                            # 如果调整后前一个字幕太短，则调整当前字幕的开始时间
                            new_curr_start = prev_start_sec + 0.3 + gap
                            
                            # 更新当前字幕的开始时间
                            curr_sub.start.hours = int(new_curr_start // 3600)
                            curr_sub.start.minutes = int((new_curr_start % 3600) // 60)
                            curr_sub.start.seconds = int(new_curr_start % 60)
                            curr_sub.start.milliseconds = int((new_curr_start - int(new_curr_start)) * 1000)
                        else:
                            # 更新前一个字幕的结束时间
                            prev_sub.end.hours = int(new_prev_end // 3600)
                            prev_sub.end.minutes = int((new_prev_end % 3600) // 60)
                            prev_sub.end.seconds = int(new_prev_end % 60)
                            prev_sub.end.milliseconds = int((new_prev_end - int(new_prev_end)) * 1000)
            
            # 保存合并后的字幕文件
            final_subs.save(output_srt, encoding='utf-8')
            print(f"字幕合并完成，共 {len(final_subs)} 条，总时长 {current_offset:.2f}秒")
            
            # ------------------ 时长一致性校验 ------------------
            # 取字幕最后一条结束时间作为字幕总时长
            if len(final_subs) > 0:
                last_sub = final_subs[-1]
                subs_total_sec = (
                    last_sub.end.hours * 3600 +
                    last_sub.end.minutes * 60 +
                    last_sub.end.seconds +
                    last_sub.end.milliseconds / 1000.0
                )
            else:
                subs_total_sec = 0

            audio_total_sec = current_offset  # 已累积的音频总时长
            diff_sec = abs(audio_total_sec - subs_total_sec)

            if diff_sec > 60:  # 超过1分钟误差
                try:
                    import tkinter as _tk
                    from tkinter import messagebox as _mb
                    _mb.showwarning(
                        "字幕与音频时长不一致",
                        f"文件 {os.path.basename(output_srt)}\n字幕总时长与音频时长相差 {diff_sec:.1f} 秒，建议重新配音。"
                    )
                except Exception:
                    # 如果在无GUI环境下，退化为打印警告
                    print(f"警告: 字幕与音频时长相差 {diff_sec:.1f} 秒 (>{60}s)，请检查 {output_srt}")
             
            # 合并完成，设置进度100%
            if progress_callback:
                progress_callback(100, "字幕合并完成")
            
        except Exception as e:
            print(f"合并字幕文件失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            raise e
    
    async def _process_batch(self, batch_index, batch_text, output_file, srt_file, semaphore, total_batches, progress_callback=None, batch_line_indices=None):
        """
        处理单个批次的文本
        只做音频合成和初步字幕生成，不做静音处理和复杂字幕同步。
        """
        # 添加到临时文件列表
        self.temp_files.append(output_file)
        # 使用信号量控制并发
        async with semaphore:
            try:
                # 检查是否被停止
                if self.stop_flag:
                    print(f"批次 {batch_index+1} 处理已取消")
                    if progress_callback:
                        progress_callback(0, "操作已取消")
                    return Exception("操作已取消")
                # 更新进度
                # 计算批次开始进度 (0-10%)
                start_progress = int((batch_index) / total_batches * 10)
                self._current_progress = start_progress
                if progress_callback:
                    progress_callback(self._current_progress, f"批次 {batch_index+1}/{total_batches} 开始 {self._current_progress}%")
                print(f"[批次处理] 开始处理批次 {batch_index+1}/{total_batches}")
                # 根据引擎选择处理方法
                engine = self.settings.get("engine", "edge-tts")
                # 仅保留 Edge TTS 引擎，其余自动回退
                if engine != "edge-tts":
                    engine = "edge-tts"
                    self.settings["engine"] = "edge-tts"
                # 设置重试参数
                max_retries = self.settings.get("retry_count", 3)
                retry_interval = self.settings.get("retry_interval", 2)
                current_retry = 0
                while True:
                    # 每次重试前检查是否已停止
                    if self.stop_flag:
                        print(f"批次 {batch_index+1} 重试已取消")
                        return Exception("操作已取消")
                    try:
                        if engine == "edge-tts":
                            # 生成音频和获取时间戳
                            timestamps = await self._edge_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            print(f"批次 {batch_index+1} 生成了 {len(timestamps)} 条字幕时间戳")
                            # 根据原始行索引调整字幕
                            timestamps = self._align_timestamps_with_original_text(timestamps, batch_line_indices)
                            # 生成字幕
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "azure-tts":
                            await self._azure_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            # 使用改进的时间戳生成方法
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "baidu-tts":
                            await self._baidu_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "xunfei-tts":
                            await self._xunfei_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        elif engine == "tencent-tts":
                            await self._tencent_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = []
                            self._generate_accurate_timestamps(batch_text, timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        else:
                            timestamps = await self._edge_tts_batch(batch_text, output_file)
                            if self.stop_flag:
                                return Exception("操作已取消")
                            timestamps = self._align_timestamps_with_original_text(timestamps, batch_line_indices)
                            await self._generate_subtitles(timestamps, srt_file)
                        # 处理成功，跳出循环
                        break
                    except Exception as e:
                        if self.stop_flag:
                            return Exception("操作已取消")
                        error_msg = str(e)
                        is_network_error = self._is_network_error(error_msg)
                        if is_network_error and current_retry < max_retries:
                            current_retry += 1
                            print(f"批次 {batch_index+1} 处理失败(网络错误): {error_msg}")
                            print(f"等待 {retry_interval} 秒后进行第 {current_retry}/{max_retries} 次重试...")
                            await asyncio.sleep(retry_interval)
                        else:
                            if is_network_error:
                                error_type = "网络错误"
                                error_detail = f"重试 {max_retries} 次后仍然失败: {error_msg}"
                            else:
                                error_type = "非网络错误"
                                error_detail = error_msg
                            print(f"批次 {batch_index+1} 处理失败({error_type}): {error_detail}")
                            raise Exception(f"处理批次 {batch_index+1} 失败({error_type}): {error_detail}")
                
                print(f"批次 {batch_index+1}/{total_batches} 处理完成")
                # 批次完成：递增计数并计算新百分比
                self._completed_batches += 1
                self._current_progress = int(10 + (self._completed_batches / total_batches) * 70)
                if progress_callback:
                    progress_callback(self._current_progress, f"批次 {self._completed_batches}/{total_batches} 完成 {self._current_progress}%")
                
                # 只保留批次级音频后处理：动态压缩/标准化/放大
                normalize_audio = self.settings.get("normalize_audio", False)
                amplify_db = float(self.settings.get("amplify_db", 0))
                
                # 先进行音频后处理（如果启用）
                if normalize_audio:
                    af_str = "acompressor=threshold=-20dB:ratio=6:attack=20:release=250,loudnorm"
                    if amplify_db != 0:
                        af_str += f",volume={amplify_db}dB"
                    print(f"批次 {batch_index+1} 动态压缩+标准化{'并放大' if amplify_db != 0 else ''}")
                    command = [
                        "ffmpeg", "-y",
                        "-i", output_file,
                        "-af", af_str,
                        "-b:a", "128k",
                        output_file + ".norm.mp3"
                    ]
                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        os.replace(output_file + ".norm.mp3", output_file)
                    else:
                        print(f"批次 {batch_index+1} 动态压缩/标准化失败")
                elif amplify_db != 0:
                    print(f"批次 {batch_index+1} 放大音量 {amplify_db} dB")
                    command = [
                        "ffmpeg", "-y",
                        "-i", output_file,
                        "-filter:a", f"volume={amplify_db}dB",
                        "-b:a", "128k",
                        output_file + ".amplified.mp3"
                    ]
                    process = await asyncio.create_subprocess_exec(
                        *command,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        startupinfo=self.startupinfo,
                        creationflags=self.creation_flags
                    )
                    await process.communicate()
                    if process.returncode == 0:
                        os.replace(output_file + ".amplified.mp3", output_file)
                    else:
                        print(f"批次 {batch_index+1} 音量放大失败")
                
            except Exception as e:
                if self.stop_flag:
                    return Exception("操作已取消")
                print(f"处理批次 {batch_index+1} 失败: {str(e)}")
                raise e
    
    async def _edge_tts_batch(self, text, output_file):
        """
        使用Edge TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
            
        返回:
            行级别的时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
        """
        try:
            # 从设置中获取参数
            voice = self.settings["voice"]
            # 修复计算逻辑，确保rate和volume使用正确的格式
            speed = self.settings['speed']
            rate = f"+{int((speed - 1) * 100)}%" if speed > 1 else f"-{int(abs((speed - 1) * 100))}%"
            if speed == 1:
                rate = "+0%"
            volume = f"+{int((self.settings['volume'] - 1) * 100)}%" if self.settings['volume'] > 1 else f"-{int(abs((self.settings['volume'] - 1) * 100))}%"
            if self.settings['volume'] == 1:
                volume = "+0%"
            
            # 修复pitch参数格式，使用Hz单位
            pitch_value = int((self.settings['pitch'] - 1) * 50)  # 将0.5-2.0的值映射到-25Hz到+50Hz
            pitch = f"+{pitch_value}Hz" if pitch_value >= 0 else f"{pitch_value}Hz"
            if self.settings['pitch'] == 1:
                pitch = "+0Hz"
            
            # 设置超时和重试参数
            timeout = 30  # 30秒超时
            connection_attempt = 0
            max_connection_attempts = 3
            
            # 用于存储字幕数据
            word_boundaries = []
            
            # 打开文件准备写入
            with open(output_file, "wb") as audio_file:
                # 添加连接重试逻辑
                while connection_attempt < max_connection_attempts:
                    try:
                        # 每次重试时创建新的通信对象，避免"stream can only be called once"错误
                        communicate = edge_tts.Communicate(text, voice, rate=rate, volume=volume, pitch=pitch)
                        
                        # 使用超时保护
                        audio_chunks = []
                        async for chunk in communicate.stream():
                            if chunk["type"] == "audio":
                                audio_data = chunk.get("data")
                                if audio_data is not None:
                                    audio_file.write(audio_data)
                                    # 记录已获取的数据块以备重试时使用
                                    audio_chunks.append(audio_data)
                            elif chunk["type"] == "WordBoundary":
                                word_boundaries.append(chunk)
                        # 如果成功完成，退出循环
                        break
                    except Exception as e:
                        connection_attempt += 1
                        error_msg = str(e)
                        
                        # 判断是否为网络错误
                        is_network_error = "timeout" in error_msg.lower() or "connection" in error_msg.lower() or "503" in error_msg
                        
                        if is_network_error and connection_attempt < max_connection_attempts:
                            print(f"Edge TTS连接错误(尝试 {connection_attempt}/{max_connection_attempts}): {error_msg}")
                            print(f"正在自动重新连接...")
                            # 短暂等待后重试
                            await asyncio.sleep(1)
                            # 如果已经有部分数据，保存已有数据
                            if audio_chunks:
                                print(f"已保存 {len(audio_chunks)} 个已下载的音频块")
                        else:
                            # 达到重试上限或非网络错误，抛出异常
                            raise
            
            # 处理字幕时间戳
            timestamps = []
            
            # 按句子分组的阈值（毫秒），随语速动态调整
            # 语速越慢，单词间停顿越长，需要更大的阈值；语速越快，则阈值应减小
            speed_factor = max(0.5, min(2.0, self.settings.get('speed', 1.0)))
            sentence_threshold = int(400 / speed_factor)  # 基准400ms，等比缩放
            
            # 分析收集到的边界信息
            if word_boundaries:
                current_text = []
                current_start = None
                current_end = None
                
                for i, boundary in enumerate(word_boundaries):
                    if "offset" in boundary and "duration" in boundary and "text" in boundary:
                        # 转换时间戳为毫秒
                        start_ms = int(boundary['offset'] / 10000)
                        duration_ms = int(boundary['duration'] / 10000)
                        end_ms = start_ms + duration_ms
                        word_text = boundary.get("text", "")
                        
                        # 如果是第一个词，开始新的句子
                        if current_start is None:
                            current_start = start_ms
                            current_text.append(word_text)
                            current_end = end_ms
                        else:
                            # 确保current_end不为None
                            if current_end is not None:
                                # 计算间隔
                                interval = start_ms - current_end
                                
                                # 如果间隔大于阈值或遇到句尾标点，结束当前句子
                                is_sentence_end = False
                                if i > 0 and "text" in word_boundaries[i-1] and word_boundaries[i-1]["text"]:
                                    is_sentence_end = word_boundaries[i-1]["text"][-1] in ",.!?;:，。！？；："
                                
                                if interval > sentence_threshold or is_sentence_end:
                                    # 保存当前句子
                                    sentence_text = "".join(current_text)
                                    # 转换为秒，确保current_start和current_end不为None
                                    if current_start is not None and current_end is not None:
                                        start_sec = current_start / 1000.0
                                        end_sec = current_end / 1000.0
                                        timestamps.append((sentence_text, start_sec, end_sec))
            
                                    # 开始新句子
                                    current_text = [word_text]
                                    current_start = start_ms
                                    current_end = end_ms
                                else:
                                    # 继续当前句子
                                    current_text.append(word_text)
                                    current_end = end_ms
                
                # 添加最后一句
                if current_text and current_start is not None and current_end is not None:
                    sentence_text = "".join(current_text)
                    # 转换为秒
                    start_sec = current_start / 1000.0
                    end_sec = current_end / 1000.0
                    timestamps.append((sentence_text, start_sec, end_sec))
            
            return timestamps
            
        except Exception as e:
            print(f"Edge TTS处理失败: {str(e)}")
            raise e    
    async def _generate_subtitles(self, timestamps, output_file):
        """
        生成SRT字幕文件
        
        参数:
            timestamps: 时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
            output_file: 输出文件路径
        """
        try:
            # 创建一个新的字幕文件
            subs = pysrt.SubRipFile()
            
            # 按顺序添加每条字幕
            for i, (line, start, end) in enumerate(timestamps):
                # 创建一个新的字幕项
                item = pysrt.SubRipItem()
                item.index = i + 1  # 字幕索引从1开始
                
                # 设置字幕内容
                item.text = line
                
                # 设置开始时间
                start_seconds = int(start)
                start_microseconds = int((start - start_seconds) * 1000000)
                item.start.hours = start_seconds // 3600
                item.start.minutes = (start_seconds % 3600) // 60
                item.start.seconds = start_seconds % 60
                item.start.milliseconds = start_microseconds // 1000
                
                # 设置结束时间
                end_seconds = int(end)
                end_microseconds = int((end - end_seconds) * 1000000)
                item.end.hours = end_seconds // 3600
                item.end.minutes = (end_seconds % 3600) // 60
                item.end.seconds = end_seconds % 60
                item.end.milliseconds = end_microseconds // 1000
                
                # 添加到字幕文件
                subs.append(item)
            
            # 保存字幕文件
            subs.save(output_file, encoding='utf-8')
            
            print(f"字幕文件已生成: {output_file}，共 {len(subs)} 条字幕")
            return True
            
        except Exception as e:
            print(f"生成字幕文件失败: {str(e)}")
            return False
    
    async def _merge_audio_files(self, output_file, audio_files):
        """
        合并音频文件
        参数:
            output_file: 输出文件路径
            audio_files: 要合并的音频文件列表
        """
        if not audio_files:
            return
        # 检查是否被停止
        if self.stop_flag:
            print("音频合并已取消")
            return
        # 过滤掉不存在的文件
        valid_audio_files = [f for f in audio_files if os.path.exists(f)]
        if not valid_audio_files:
            print("没有有效的音频文件可以合并")
            return
        temp_dir = os.path.join(os.path.dirname(output_file), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        # 检查是否被停止
        if self.stop_flag:
            print("音频合并已取消")
            return
        # 如果只有一个文件，直接处理
        if len(valid_audio_files) == 1:
            try:
                if self.stop_flag:
                    print("单文件处理已取消")
                    return
                shutil.copy2(valid_audio_files[0], output_file)
                print("单个文件处理完成")
            except Exception as e:
                print(f"音频处理出错: {str(e)}")
                if not self.stop_flag:
                    shutil.copy2(valid_audio_files[0], output_file)
            return
        # 多文件处理逻辑
        if self.stop_flag:
            print("多文件合并已取消")
            return
        file_list_path = os.path.join(temp_dir, "files.txt")
        base_name = os.path.splitext(os.path.basename(output_file))[0]
        renamed_files = []
        for i, audio_file in enumerate(valid_audio_files):
            if self.stop_flag:
                print("文件重命名过程已取消")
                return
            new_name = os.path.join(temp_dir, f"{base_name}-片段{i+1}.mp3")
            if audio_file != new_name:
                shutil.copy2(audio_file, new_name)
            renamed_files.append(new_name)
            self.temp_files.append(new_name)
        if self.stop_flag:
            print("文件列表创建已取消")
            return
        with open(file_list_path, "w", encoding="utf-8") as f:
            for file_path in renamed_files:
                f.write(f"file '{file_path.replace(os.sep, '/')} '\n")
        self.temp_files.append(file_list_path)
        temp_merged = os.path.join(temp_dir, "temp_merged.mp3")
        try:
            if self.stop_flag:
                print("FFmpeg合并过程已取消")
                return
            command = [
                "ffmpeg",
                "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", file_list_path,
                "-c", "copy",
                "-b:a", "128k",
                output_file
            ]
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self.startupinfo,
                creationflags=self.creation_flags
            )
            stdout, stderr = await process.communicate()
            if self.stop_flag:
                print("音频合并已取消")
                return
            if process.returncode != 0:
                print("文件合并失败，尝试直接连接文件")
                print(f"FFmpeg错误: {stderr.decode('utf-8', errors='replace')}")
                with open(output_file, 'wb') as outfile:
                    for file_path in renamed_files:
                        if self.stop_flag:
                            print("直接连接文件已取消")
                            return
                        if os.path.exists(file_path):
                            with open(file_path, 'rb') as infile:
                                outfile.write(infile.read())
            else:
                print("多个文件合并完成")
        except Exception as e:
            print(f"音频合并失败: {str(e)}")
            if self.stop_flag:
                print("出错后处理已取消")
                return
            try:
                with open(output_file, 'wb') as outfile:
                    for file_path in renamed_files:
                        if self.stop_flag:
                            print("最终连接处理已取消")
                            return
                        if os.path.exists(file_path):
                            with open(file_path, 'rb') as infile:
                                outfile.write(infile.read())
            except Exception as e2:
                if not self.stop_flag:
                    print(f"所有音频处理方法都失败: {str(e2)}")
                    raise Exception("音频处理失败")
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        print("开始清理临时文件...")
        
        # 创建要删除的文件列表的副本
        files_to_delete = self.temp_files.copy()
        self.temp_files = []
        
        # 记录删除成功和失败的文件数量
        deleted_count = 0
        failed_count = 0
        
        for file_path in files_to_delete:
            try:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except PermissionError:
                        print(f"无法删除文件(权限不足): {file_path}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
                    except Exception as e:
                        print(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
                        # 将无法删除的文件添加回列表，稍后再尝试删除
                        self.temp_files.append(file_path)
                        failed_count += 1
            except Exception as e:
                print(f"检查文件时出错: {file_path}, 错误: {str(e)}")
                failed_count += 1
        
        if deleted_count > 0:
            print(f"已删除 {deleted_count} 个临时文件" + (f"，{failed_count} 个文件删除失败" if failed_count > 0 else ""))
        
        # 尝试清理空的临时目录
        try:
            # 查找所有temp目录
            temp_dirs = set()
            
            # 从当前剩余的临时文件路径中提取目录
            for file_path in self.temp_files:
                dir_path = os.path.dirname(file_path)
                if os.path.basename(dir_path) == "temp" and os.path.exists(dir_path):
                    temp_dirs.add(dir_path)
            
            # 增加额外的检测 - 在已删除文件的路径中查找temp目录
            for file_path in files_to_delete:
                dir_path = os.path.dirname(file_path)
                if os.path.basename(dir_path) == "temp" and os.path.exists(dir_path):
                    temp_dirs.add(dir_path)
            
            # 统计删除的目录数
            deleted_dirs = 0
            
            # 遍历所有可能的temp目录，尝试删除空目录
            for temp_dir in temp_dirs:
                try:
                    if os.path.exists(temp_dir):
                        # 检查目录是否为空
                        if not os.listdir(temp_dir):
                            os.rmdir(temp_dir)
                            deleted_dirs += 1
                            print(f"已删除空的临时目录: {temp_dir}")
                        else:
                            # 目录非空，可能是其他进程正在使用，或者有无法删除的文件
                            if self.stop_flag:
                                # 如果是在停止操作中，尝试强制删除所有文件
                                try:
                                    for file_name in os.listdir(temp_dir):
                                        file_path = os.path.join(temp_dir, file_name)
                                        try:
                                            if os.path.isfile(file_path):
                                                os.remove(file_path)
                                            elif os.path.isdir(file_path):
                                                shutil.rmtree(file_path, ignore_errors=True)
                                        except Exception as e:
                                            print(f"强制清理时无法删除: {file_path}, 错误: {str(e)}")
                                    
                                    # 再次尝试删除目录
                                    if not os.listdir(temp_dir):
                                        os.rmdir(temp_dir)
                                        deleted_dirs += 1
                                        print(f"已删除清理后的临时目录: {temp_dir}")
                                except Exception as e:
                                    print(f"强制清理目录失败: {temp_dir}, 错误: {str(e)}")
                except Exception as e:
                    print(f"删除临时目录失败: {temp_dir}, 错误: {str(e)}")
            
            if deleted_dirs > 0:
                print(f"已删除 {deleted_dirs} 个临时目录")
                
        except Exception as e:
            print(f"清理临时目录过程中出错: {str(e)}")
        
        # 返回清理结果
        return {
            "deleted_files": deleted_count,
            "failed_files": failed_count
        }
    
    def stop(self):
        """停止当前处理"""
        print("TTS引擎: 正在执行停止操作...")
        self.stop_flag = True
        # 取消所有批量任务
        for task in getattr(self, '_batch_tasks', []):
            if not task.done():
                task.cancel()
        # 立即清理未完成的临时文件，避免等到对象销毁时才清理
        self._cleanup_temp_files()
        
    def __del__(self):
        """析构函数，清理资源"""
        # 清理临时文件
        self._cleanup_temp_files()
        
        # 关闭事件循环
        if self.loop and not self.loop.is_closed():
            self.loop.close()

    def _generate_basic_timestamps(self, text, timestamps):
        """
        为不提供详细时间戳的TTS引擎生成基本时间戳
        
        参数:
            text: 文本内容
            timestamps: 时间戳列表
        """
        # 按行分割文本
        lines = text.splitlines()
        total_chars = sum(len(line) for line in lines)
        
        # 假设每个字符平均时长为0.2秒
        char_duration = 0.2
        current_time = 0
        
        for line in lines:
            if not line.strip():
                continue
                
            # 计算当前行预计时长
            line_duration = len(line) * char_duration
            start_time = current_time
            end_time = start_time + line_duration
            
            # 添加时间戳
            timestamps.append((line, start_time, end_time))
            
            # 更新当前时间
            current_time = end_time

    async def _azure_tts_batch(self, text, output_file):
        """
        使用Azure TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._azure_tts_preview(text, output_file)

    async def _baidu_tts_batch(self, text, output_file):
        """
        使用百度TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._baidu_tts_preview(text, output_file)

    async def _xunfei_tts_batch(self, text, output_file):
        """
        使用讯飞TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._xunfei_tts_preview(text, output_file)

    async def _tencent_tts_batch(self, text, output_file):
        """
        使用腾讯TTS处理批次文本
        
        参数:
            text: 文本内容
            output_file: 输出文件路径
        """
        # 直接复用预览方法
        await self._tencent_tts_preview(text, output_file)

    def _is_network_error(self, error_msg):
        """判断是否为网络错误"""
        network_keywords = ['connection', 'timeout', '503', 'network', 
                           '连接', '超时', '网络', '断开', 'refused', '拒绝']
        error_lower = error_msg.lower()
        return any(keyword in error_lower for keyword in network_keywords)

    async def _get_audio_duration(self, audio_file):
        """
        获取音频文件的时长（秒）
        
        参数:
            audio_file: 音频文件路径
            
        返回:
            音频时长（秒）
        """
        try:
            # 使用ffprobe获取音频时长
            command = [
                "ffprobe",
                "-v", "error",
                "-threads", str(self.settings.get("concurrent_tasks", 4)),
                "-thread_queue_size", "512",  # 增加线程队列大小
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                audio_file
            ]
            
            # 创建进程
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                startupinfo=self.startupinfo,
                creationflags=self.creation_flags
            )
            
            # 等待进程完成
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                # 如果ffprobe失败，使用备选方法
                return self._estimate_audio_duration(audio_file)
            
            # 解析输出获取时长
            duration = float(stdout.decode('utf-8').strip())
            
            # 验证时长的有效性
            if duration <= 0:
                print(f"警告: ffprobe返回的音频时长为{duration}秒，使用估算方法")
                return self._estimate_audio_duration(audio_file)
                
            return duration
            
        except Exception as e:
            print(f"获取音频时长失败: {str(e)}")
            # 使用备选方法估算时长
            return self._estimate_audio_duration(audio_file)
    
    def _estimate_audio_duration(self, audio_file):
        """
        估算MP3文件时长（备选方法）
        
        参数:
            audio_file: 音频文件路径
            
        返回:
            估算的音频时长（秒）
        """
        try:
            # 获取文件大小（字节）
            file_size = os.path.getsize(audio_file)
            
            # MP3文件大致比特率（比特/秒）
            bitrate = 128000  # 假设128kbps的比特率
            
            # 估算时长：文件大小（位）/ 比特率
            estimated_duration = (file_size * 8) / bitrate
            
            # 添加一个小的安全余量
            estimated_duration += 0.1
            
            return estimated_duration
        except Exception as e:
            print(f"估算音频时长失败: {str(e)}")
            # 返回基于文件内最后一个字幕的时长
            try:
                srt_file = audio_file.replace('.mp3', '.srt')
                if os.path.exists(srt_file):
                    subs = pysrt.open(srt_file, encoding='utf-8')
                    if subs and len(subs) > 0:
                        last_sub = subs[-1]
                        duration = (last_sub.end.hours * 3600 + last_sub.end.minutes * 60 + 
                                last_sub.end.seconds + last_sub.end.milliseconds / 1000)
                        # 添加一个小的安全余量
                        return duration + 0.1
            except Exception as err:
                print(f"从字幕估算时长失败: {str(err)}")
            
            # 如果所有方法都失败，则返回一个默认值
            return 5.0  # 默认5秒 

    def _align_timestamps_with_original_text(self, timestamps, batch_line_indices):
        """
        根据原始行索引调整字幕时间戳，确保字幕内容与原文匹配
        
        参数:
            timestamps: 时间戳列表，每个元素为(行文本, 开始时间, 结束时间)
            batch_line_indices: 批次中原始行索引列表
            
        返回:
            调整后的时间戳列表
        """
        if not batch_line_indices:
            return timestamps
            
        aligned_timestamps = []
        
        # 创建原始行内容到时间戳的映射
        # 使用文本相似度匹配，确保即使有细微差异也能匹配上
        text_to_timestamp = {}
        
        # 对于Edge TTS返回的时间戳，可能会有一些细微的差异
        # 例如标点符号可能会被省略或替换
        for text, start, end in timestamps:
            text_to_timestamp[text] = (start, end)
        
        # 遍历批次中的每个原始行索引
        for idx in batch_line_indices:
            if idx in self.original_text_mapping:
                original_line = self.original_text_mapping[idx]
                
                # 如果原始行为空，跳过
                if not original_line.strip():
                    continue
                
                # 尝试在时间戳中找到完全匹配的行
                if original_line in text_to_timestamp:
                    start, end = text_to_timestamp[original_line]
                    aligned_timestamps.append((original_line, start, end))
                else:
                    # 如果找不到完全匹配，尝试找到最相似的行
                    best_match = None
                    best_similarity = 0
                    
                    for gen_text in text_to_timestamp:
                        # 计算简单的相似度（共同字符数量 / 较长文本的长度）
                        common_chars = sum(1 for c in gen_text if c in original_line)
                        similarity = common_chars / max(len(gen_text), len(original_line))
                        
                        if similarity > best_similarity:
                            best_similarity = similarity
                            best_match = gen_text
                    
                    # 如果相似度大于0.7，认为找到了匹配
                    if best_match and best_similarity > 0.7:
                        start, end = text_to_timestamp[best_match]
                        aligned_timestamps.append((original_line, start, end))
                    else:
                        # 如果没有找到足够相似的匹配，使用估算的时间
                        # 根据行的长度和位置估算
                        if aligned_timestamps:
                            # 如果已经有之前的行，使用之前行的结束时间作为当前行的开始时间
                            prev_end = aligned_timestamps[-1][2]
                            # 估算当前行的持续时间（0.2秒每个字符）
                            duration = len(original_line) * 0.2
                            aligned_timestamps.append((original_line, prev_end, prev_end + duration))
                        else:
                            # 如果是第一行，从0开始
                            duration = len(original_line) * 0.2
                            aligned_timestamps.append((original_line, 0, duration))
        
        # 如果没有找到任何匹配，直接返回原始时间戳
        if not aligned_timestamps and timestamps:
            print("警告：未能将任何字幕与原文对齐，使用原始时间戳")
            return timestamps
            
        return aligned_timestamps

    def _generate_accurate_timestamps(self, text, timestamps, batch_line_indices):
        """
        为非Edge TTS引擎生成更准确的时间戳
        
        参数:
            text: 文本内容
            timestamps: 时间戳列表（将被填充）
            batch_line_indices: 批次中原始行索引列表
        """
        # 这个方法用于为其他TTS引擎生成更准确的时间戳
        
        # 首先尝试使用FFmpeg分析音频文件中的静音部分来确定段落边界
        # 如果没有音频文件或分析失败，则使用改进的估算方法
        
        # 按行分割文本
        lines = text.splitlines()
        
        # 过滤空行
        non_empty_lines = [(i, line) for i, line in enumerate(lines) if line.strip()]
        
        if not non_empty_lines:
            return
            
        # 计算总字符数
        total_chars = sum(len(line) for _, line in non_empty_lines)
        
        # 根据字符数估算每个字符的平均时长
        # 假设每个汉字约需0.3秒，每个字母约需0.1秒
        def estimate_duration(text):
            # 计算汉字数量
            chinese_chars = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
            # 计算字母和数字数量
            alphanumeric = sum(1 for c in text if c.isalnum() and not '\u4e00' <= c <= '\u9fff')
            # 估算总时长
            return chinese_chars * 0.3 + alphanumeric * 0.1 + 0.2  # 额外0.2秒作为句子间隔
        
        # 估算总时长
        total_duration = sum(estimate_duration(line) for _, line in non_empty_lines)
        
        # 设置当前时间点
        current_time = 0
        
        # 为每行生成时间戳
        for i, (orig_idx, line) in enumerate(non_empty_lines):
            if not line.strip():
                continue
                
            # 估算当前行的时长
            line_duration = estimate_duration(line)
            
            # 设置开始和结束时间
            start_time = current_time
            end_time = start_time + line_duration
            
            # 添加到时间戳列表
            timestamps.append((line, start_time, end_time))
            
            # 更新当前时间
            current_time = end_time
            
            # 如果不是最后一行，添加小的间隔
            if i < len(non_empty_lines) - 1:
                current_time += 0.1

"""
高级设置界面模块，用于显示和管理高级配音设置
"""
# =================== 高级设置界面模块 ===================

class AdvancedSettingsFrame(ttk.Frame):
    """高级设置界面，显示高级配音设置"""

    def __init__(self, parent):
        """
        初始化高级设置界面
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 设置密码
        self.password = "1294174256"
        
        # 设置是否已解锁
        self.is_unlocked = False
        
        # 解锁的有效时间（秒）
        self.unlock_duration = 300  # 5分钟
        
        # 解锁的过期时间
        self.unlock_expiry = 0
        
        # 1. 初始化 settings 字典的默认值
        self._initialize_default_settings_dict()
        
        # 2. 初始化Tkinter变量 (使用默认的 self.settings 值)
        self.init_variables()
        
        # 3. 尝试从文件加载设置，如果成功则更新 self.settings 和 Tkinter 变量
        self._load_settings_from_file_and_update_ui_vars()
        
        # 4. 创建界面组件
        self.create_widgets()
        
        # 5. 初始时检查锁定状态并更新界面
        self.update_ui_lock_status()
        
    def _initialize_default_settings_dict(self):
        """初始化高级设置的默认字典 self.settings 及 UI 相关的 option 列表"""
        # 默认设置
        self.settings = {
            # 处理选项
            "concurrent_tasks": 6,  # 并发任务数
            "retry_count": 5,  # 重试次数
            "retry_interval": 10,  # 重试间隔（秒）
            "batch_size": 3000,  # 分批大小（字符数），修改为3000
            # 文本处理
            "remove_empty_lines": True,  # 去除空行
        }
        
        # 并发任务数选项列表
        self.concurrent_tasks_options = [
            "2",    # 2个任务
            "3",    # 3个任务
            "5",    # 5个任务
            "6",    # 6个任务
            "8",    # 8个任务
            "10",   # 10个任务
            "12",   # 12个任务
            "16",   # 16个任务
            "20",   # 20个任务
        ]
        
        # 重试次数选项列表
        self.retry_count_options = [
            "5",    # 5次
            "10",   # 10次
            "15",   # 15次
            "20",   # 20次
        ]
        
    def init_variables(self):
        """初始化Tkinter变量"""
        # 处理选项变量
        self.concurrent_var = tk.StringVar(value=str(self.settings["concurrent_tasks"]))
        self.retry_count_var = tk.StringVar(value="无限" if self.settings["retry_count"] == -1 else str(self.settings["retry_count"]))
        self.retry_interval_var = tk.IntVar(value=self.settings["retry_interval"])
        self.batch_size_var = tk.IntVar(value=self.settings["batch_size"])
        self.remove_empty_var = tk.BooleanVar(value=self.settings["remove_empty_lines"])
        
        # 锁定状态变量
        self.lock_status_var = tk.StringVar(value="🔒 设置已锁定")
        
    def _load_settings_from_file_and_update_ui_vars(self):
        """
        从文件加载设置。
        如果成功，则更新 self.settings 字典，并接着更新对应的Tkinter UI变量。
        """
        try:
            settings_dir = os.path.expanduser("~/.tts_tool")
            settings_file = os.path.join(settings_dir, "advanced_settings.json")
            
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)
                    
                # 更新 self.settings 字典
                for key, value in loaded_settings.items():
                    if key in self.settings:
                        self.settings[key] = value
                
                # self.settings 已更新，现在用这些值更新 Tkinter 变量
                # (这些变量已在 self.init_variables() 中基于默认值创建)
                self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
                self.retry_count_var.set("无限" if self.settings["retry_count"] == -1 else str(self.settings["retry_count"]))
                self.retry_interval_var.set(self.settings["retry_interval"])
                self.batch_size_var.set(self.settings["batch_size"])
                self.remove_empty_var.set(self.settings["remove_empty_lines"])
                
                print("成功从文件加载高级设置并更新了UI变量。")
            else:
                print("高级设置文件不存在，将使用默认设置。")
        except Exception as e:
            print(f"加载高级设置文件或更新UI变量时发生错误: {str(e)}")
            # 如果加载或更新UI变量失败，Tkinter变量将保留它们在init_variables()中设置的初始值（基于默认settings）。
            # self.settings 字典可能部分更新或保持默认。
            pass

    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, pady=(10, 20))
        
        ttk.Label(title_frame, text="高级设置", 
                 font=("微软雅黑", 16, "bold"),
                 foreground="#1565C0").pack(anchor=tk.CENTER)
        
        # 分隔线
        ttk.Separator(self, orient="horizontal").pack(fill=tk.X, padx=20, pady=5)
        
        # 设置区域容器 - 使用居中布局
        self.main_container = ttk.Frame(self)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=50, pady=10)
        
        # 创建设置组 - 处理选项
        process_frame = self.create_section_frame("处理选项")
        
        # 批次大小设置（锁定状态）
        ttk.Label(process_frame, text="批次大小(字):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.batch_entry = ttk.Entry(process_frame, textvariable=self.batch_size_var, width=12, state="readonly")
        self.batch_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 并发任务数设置（改为下拉列表）
        ttk.Label(process_frame, text="并发任务数(最大20):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.tasks_combo = ttk.Combobox(process_frame, textvariable=self.concurrent_var, 
                                      values=self.concurrent_tasks_options, 
                                      state="readonly", width=10)
        self.tasks_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 添加并发任务数下拉框的绑定，当用户尝试选择时检查锁定状态
        self.tasks_combo.bind("<<ComboboxSelected>>", lambda event: self.on_concurrent_tasks_change(event))
        
        # 锁定状态显示和解锁按钮
        lock_frame = ttk.Frame(process_frame)
        lock_frame.grid(row=0, column=2, rowspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 锁定状态标签
        lock_status = ttk.Label(lock_frame, textvariable=self.lock_status_var)
        lock_status.pack(anchor=tk.W, pady=(0, 5))
        
        # 解锁/锁定按钮
        self.lock_btn = ttk.Button(lock_frame, text="解锁设置", width=8, command=self.toggle_lock)
        self.lock_btn.pack(anchor=tk.W)
        
        # 创建设置组 - 文本处理选项
        text_frame = self.create_section_frame("文本处理")
        
        # 去除空行选项
        remove_empty_check = ttk.Checkbutton(text_frame, text="去除空行", 
                                          variable=self.remove_empty_var)
        remove_empty_check.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5, columnspan=2)
        
        # 按钮区域
        button_frame = ttk.Frame(self.main_container)
        button_frame.pack(fill=tk.X, pady=20)
        
        # 保存设置按钮
        save_btn = ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=15)
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 重置设置按钮
        reset_btn = ttk.Button(button_frame, text="重置设置", command=self.reset_settings, width=15)
        reset_btn.pack(side=tk.RIGHT, padx=5)
    
    def toggle_lock(self):
        """切换锁定状态"""
        current_time = time.time()
        
        # 如果已解锁但已过期，重置解锁状态
        if self.is_unlocked and current_time > self.unlock_expiry:
            self.is_unlocked = False
            
        if self.is_unlocked:
            # 如果已解锁，则锁定
            self.lock_settings()
        else:
            # 如果已锁定，则尝试解锁
            self.unlock_settings()
    
    def unlock_settings(self):
        """解锁设置"""
        # 弹出密码输入对话框
        password = simpledialog.askstring("密码", 
                                        "请输入密码以解锁设置:", 
                                        show="*")
        
        # 验证密码
        if password == self.password:
            # 密码正确，解锁设置
            self.is_unlocked = True
            self.unlock_expiry = time.time() + self.unlock_duration
            
            # 更新UI
            self.update_ui_lock_status()
            
            # 显示解锁成功消息
            messagebox.showinfo("解锁成功", f"设置已解锁，{int(self.unlock_duration/60)}分钟内可以自由修改")
        else:
            # 密码错误
            messagebox.showerror("错误", "密码错误，无法解锁设置")
    
    def lock_settings(self):
        """锁定设置"""
        # 锁定状态
        self.is_unlocked = False
        
        # 更新UI
        self.update_ui_lock_status()
    
    def check_lock_status(self):
        """检查锁定状态，如果已过期则自动锁定"""
        if self.is_unlocked and time.time() > self.unlock_expiry:
            self.is_unlocked = False
            # 更新UI显示
            self.update_ui_lock_status()
            
        # 返回当前锁定状态
        return self.is_unlocked
        
    def create_section_frame(self, title):
        """创建带标题的设置分组"""
        # 创建分组容器
        section_frame = ttk.LabelFrame(self.main_container, text=title)
        section_frame.pack(fill=tk.X, pady=(10, 15), padx=5)
        
        return section_frame
    
    def on_setting_change(self, *args):
        """当设置改变时回调这个函数"""
        # 检查锁定状态
        if not self.check_lock_status():
            return
            
        # 处理重试次数的特殊情况
        retry_value = self.retry_count_var.get()
        if retry_value == "无限":
            retry_count = -1
        else:
            try:
                retry_count = int(retry_value)
            except ValueError:
                retry_count = 5
                
        # 处理并发任务数，确保转换为整数并限制最大值为20
        try:
            concurrent_tasks = int(self.concurrent_var.get())
            # 限制最大值为20
            if concurrent_tasks > 20:
                concurrent_tasks = 20
                self.concurrent_var.set(str(concurrent_tasks))
                messagebox.showinfo("提示", "并发任务数已限制为最大值20")
        except ValueError:
            concurrent_tasks = 6  # 默认值
            self.concurrent_var.set(str(concurrent_tasks))
        
        # 更新设置
        self.settings.update({
            "concurrent_tasks": concurrent_tasks,
            "retry_count": retry_count,
            "retry_interval": self.retry_interval_var.get(),
            "batch_size": self.batch_size_var.get(),
            "remove_empty_lines": self.remove_empty_var.get(),
        })
        
        return self.settings
    
    def on_concurrent_tasks_change(self, event=None):
        """处理并发任务数变更"""
        # 检查锁定状态
        if not self.check_lock_status():
            # 如果未解锁，弹出提示并还原值
            messagebox.showinfo("提示", "请先解锁设置再修改并发任务数")
            # 还原为之前的值
            self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
            return
        
        # 如果已解锁，则允许更改并更新设置
        self.on_setting_change()
    
    def save_settings(self):
        """保存设置到文件"""
        # 更新设置
        self.on_setting_change()
        
        try:
            # 获取设置文件路径
            settings_dir = os.path.expanduser("~/.tts_tool")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            # 新的配置文件名
            settings_file = os.path.join(settings_dir, "tts_advanced_config.json")

            # 检查是否存在旧的配置文件，如果存在则迁移
            old_settings_file = os.path.join(settings_dir, "advanced_settings.json")
            if os.path.exists(old_settings_file) and not os.path.exists(settings_file):
                try:
                    # 迁移旧配置文件到新文件名
                    import shutil
                    shutil.copy2(old_settings_file, settings_file)
                    print("已迁移旧的高级设置配置文件到新文件名")
                    # 删除旧文件
                    os.remove(old_settings_file)
                    print("已删除旧的配置文件")
                except Exception as e:
                    print(f"迁移配置文件失败: {str(e)}")
                    # 如果迁移失败，仍使用新文件名
            
            # 保存为JSON
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(self.settings, f, indent=4)
            
            # 显示成功消息
            messagebox.showinfo("保存成功", "高级设置已保存")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("保存失败", f"保存设置失败: {str(e)}")
    
    def reset_settings(self):
        """重置高级设置为默认值"""
        if not messagebox.askyesno("确认", "确定要重置所有高级设置为默认值吗？"):
            return
            
        # 重置为默认设置
        self.settings = {
            "concurrent_tasks": 6,
            "retry_count": 5,
            "retry_interval": 10,
            "batch_size": 10000,
            "remove_empty_lines": True,
        }
        
        # 更新UI变量
        self.concurrent_var.set(str(self.settings["concurrent_tasks"]))
        self.retry_count_var.set(str(self.settings["retry_count"]))
        self.retry_interval_var.set(self.settings["retry_interval"])
        self.batch_size_var.set(self.settings["batch_size"])
        self.remove_empty_var.set(self.settings["remove_empty_lines"])
        
        # 更新界面
        self.update_ui_lock_status()
        
        # 显示消息
        messagebox.showinfo("重置成功", "高级设置已重置为默认值")
    
    def get_settings(self):
        """获取当前高级设置"""
        # 更新设置
        settings = {
            "concurrent_tasks": int(self.concurrent_var.get()),
            "retry_count": -1 if self.retry_count_var.get() == "无限" else int(self.retry_count_var.get()),
            "retry_interval": self.retry_interval_var.get(),
            "batch_size": self.batch_size_var.get(),
            "remove_empty_lines": self.remove_empty_var.get(),
        }
        
        return settings

    def update_ui_lock_status(self):
        """检查锁定状态并更新界面"""
        if self.check_lock_status():
            self.batch_entry.configure(state="normal")
            self.tasks_combo.configure(state="readonly")
            self.lock_status_var.set("🔓 设置已解锁")
            self.lock_btn.configure(text="锁定设置")
        else:
            self.batch_entry.configure(state="readonly")
            self.tasks_combo.configure(state="disabled")
            self.lock_status_var.set("🔒 设置已锁定")
            self.lock_btn.configure(text="解锁设置") 
            
"""
主应用程序类，管理所有界面组件
"""
# =================== 主应用程序模块 ===================

class Application:
    """主应用程序类，负责管理整个应用的组件和交互"""

    def __init__(self, root):
        """
        初始化应用程序
        
        参数:
            root: tkinter根窗口
        """
        self.root = root
        self.current_frame = None
        self.current_function = "single"  # 默认为单独配音功能
        
        # 初始化界面
        self.setup_ui()
        
    def setup_ui(self):
        """设置应用程序界面"""
        # 设置整体样式
        self.setup_styles()
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建三列布局 - 固定左右宽度，中间自适应
        self.left_panel = ttk.Frame(self.main_frame, width=100, style="LeftPanel.TFrame")
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, expand=False)
        # 让宽度固定
        self.left_panel.pack_propagate(False)
        
        # 中间内容区域 - 添加边框效果
        self.center_container = ttk.Frame(self.main_frame)
        self.center_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        self.center_panel = ttk.Frame(self.center_container, style="CenterPanel.TFrame")
        self.center_panel.pack(fill=tk.BOTH, expand=True)
        
        # 右侧设置面板 - 添加边框效果
        self.right_panel = ttk.Frame(self.main_frame, width=300, style="RightPanel.TFrame")
        self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, expand=False, padx=2, pady=2)
        # 让宽度固定
        self.right_panel.pack_propagate(False)
        
        # 设置左侧功能选择按钮
        self.setup_function_buttons()
        
        # 添加分隔线
        self.add_separators()
        
        # 设置右侧设置面板
        self.settings_frame = SettingsFrame(self.right_panel)
        self.settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 默认显示单独配音功能
        self.show_function("single")
    
    def setup_styles(self):
        """设置应用程序样式"""
        style = ttk.Style()
        
        # 设置全局字体
        default_font = ("微软雅黑", 10)
        style.configure(".", font=default_font)
        
        # 设置按钮样式 - 更现代的扁平设计
        style.configure("Function.TButton", 
                        font=("微软雅黑", 11), 
                        padding=(5, 8),  # 减小水平内边距
                        width=10,
                        relief=tk.FLAT,
                        background="#2196F3")
        
        style.map("Function.TButton",
                 background=[('active', '#1976D2'), ('pressed', '#0D47A1')])
        
        # 设置选中按钮样式
        style.configure("ActiveFunction.TButton", 
                        font=("微软雅黑", 11, "bold"), 
                        padding=(5, 8), # 减小水平内边距
                        width=10,
                        background="#1565C0")
        
        style.map("ActiveFunction.TButton",
                 background=[('active', '#0D47A1'), ('pressed', '#0D47A1')])
        
        # 设置面板样式
        style.configure("LeftPanel.TFrame", background="#263238")  # 深色背景
        style.configure("CenterPanel.TFrame", background="#FFFFFF") # 中间内容区为白色
        style.configure("RightPanel.TFrame", background="#F5F5F5") # 右侧设置区为淡灰色
        
        # 设置带边框的容器样式
        style.configure("TableContainer.TFrame", 
                        relief=tk.GROOVE, 
                        borderwidth=1)
        
        style.configure("PathContainer.TFrame", 
                        relief=tk.SUNKEN, 
                        borderwidth=1)
        
        # 左侧面板标签样式
        style.configure("LeftPanel.TLabel", 
                        background="#263238", 
                        foreground="#FFFFFF",
                        font=("微软雅黑", 12))
        
        # 进度条样式
        style.configure("TProgressbar", 
                        thickness=15,
                        background="#2196F3")
        
        # 表格样式
        style.configure("Treeview", 
                        background="#FFFFFF",
                        fieldbackground="#FFFFFF",
                        foreground="#333333",
                        rowheight=25)
        
        style.configure("Treeview.Heading", 
                        font=("微软雅黑", 10, "bold"),
                        background="#E1E1E1")
        
        style.map("Treeview",
                 background=[('selected', '#E3F2FD')],
                 foreground=[('selected', '#0D47A1')])
        
        # 设置输入框样式
        style.configure("TEntry", padding=3)
        style.map("TEntry",
                 fieldbackground=[('focus', '#E3F2FD')])
        
        # 设置数值显示输入框样式
        style.configure("ValueDisplay.TEntry", 
                       font=("微软雅黑", 11, "bold"), # 增大字体
                       foreground="#1565C0",
                       background="#E1F5FE", # 使用淡蓝色背景
                       borderwidth=1,
                       relief="solid",
                       padding=(2,1)) # 调整内边距适应小空间
        
        # 设置下拉框样式
        style.configure("TCombobox", padding=3)
        
        # 设置分隔线样式
        style.configure("Separator.TFrame", 
                        background="#CCCCCC", 
                        height=1)
                        
    def setup_function_buttons(self):
        """设置左侧功能选择按钮"""
        # 添加标题
        title_label = ttk.Label(self.left_panel, 
                               text="配音工具", 
                               style="LeftPanel.TLabel",
                               font=("微软雅黑", 14, "bold"))
        title_label.pack(pady=(20, 30), padx=5)
        
        # 创建按钮容器帧，用于垂直排列按钮
        button_frame = ttk.Frame(self.left_panel, style="LeftPanel.TFrame")
        button_frame.pack(fill=tk.BOTH, expand=True)
        
        # 单独配音按钮
        self.single_btn = ttk.Button(button_frame, 
                                    text="单独配音", 
                                    style="Function.TButton",
                                    command=lambda: self.show_function("single"))
        self.single_btn.pack(pady=10, padx=10, fill=tk.X)
        
        # 批量配音按钮
        self.batch_btn = ttk.Button(button_frame, 
                                   text="批量配音", 
                                   style="Function.TButton",
                                   command=lambda: self.show_function("batch"))
        self.batch_btn.pack(pady=10, padx=10, fill=tk.X)
        
        # 说明按钮
        self.instruction_btn = ttk.Button(button_frame, 
                                         text="说明", 
                                         style="Function.TButton",
                                         command=lambda: self.show_function("instruction"))
        self.instruction_btn.pack(pady=10, padx=10, fill=tk.X)
        
        # 高级设置按钮
        self.advanced_btn = ttk.Button(button_frame, 
                                      text="高级设置", 
                                      style="Function.TButton",
                                      command=lambda: self.show_function("advanced"))
        self.advanced_btn.pack(pady=10, padx=10, fill=tk.X)
        
        # 底部版权信息
        version_label = ttk.Label(self.left_panel, 
                                text="v1.0", 
                                style="LeftPanel.TLabel",
                                font=("微软雅黑", 9))
        version_label.pack(pady=(0, 20), padx=5, side=tk.BOTTOM)
    
    def show_function(self, function_name):
        """
        切换显示指定的功能
        
        参数:
            function_name: 功能名称，可选 "single", "batch", "instruction", "advanced"
        """
        # 记录当前功能
        self.current_function = function_name
        
        # 如果有当前显示的功能框架，先移除
        if self.current_frame is not None:
            self.current_frame.pack_forget()
        
        # 根据功能名称显示对应的界面
        if function_name == "single":
            if not hasattr(self, 'single_frame'):
                self.single_frame = SingleTTSFrame(self.center_panel, self.settings_frame)
            self.current_frame = self.single_frame
            # 确保右侧面板可见
            self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, expand=False, padx=2, pady=2)
            
        elif function_name == "batch":
            if not hasattr(self, 'batch_frame'):
                self.batch_frame = BatchTTSFrame(self.center_panel, self.settings_frame)
            self.current_frame = self.batch_frame
            # 确保右侧面板可见，与单独配音界面相同的显示设置
            self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, expand=False, padx=2, pady=2)
            
        elif function_name == "instruction":
            if not hasattr(self, 'instruction_frame'):
                self.instruction_frame = InstructionFrame(self.center_panel)
            self.current_frame = self.instruction_frame
            self.right_panel.pack_forget()  # 说明界面不显示右侧设置
            
        elif function_name == "advanced":
            if not hasattr(self, 'advanced_frame'):
                self.advanced_frame = AdvancedSettingsFrame(self.center_panel)
            self.current_frame = self.advanced_frame
            self.right_panel.pack_forget()  # 高级设置界面不显示右侧设置
        
        # 显示当前功能框架
        if self.current_frame is not None:
            self.current_frame.pack(fill=tk.BOTH, expand=True)
        
        # 更新按钮状态
        self.update_button_states()
    
    def update_button_states(self):
        """更新功能按钮的状态"""
        # 重置所有按钮样式
        self.single_btn.configure(style="Function.TButton")
        self.batch_btn.configure(style="Function.TButton")
        self.instruction_btn.configure(style="Function.TButton")
        self.advanced_btn.configure(style="Function.TButton")
        
        # 设置当前选中的按钮样式
        if self.current_function == "single":
            self.single_btn.configure(style="ActiveFunction.TButton")
        elif self.current_function == "batch":
            self.batch_btn.configure(style="ActiveFunction.TButton")
        elif self.current_function == "instruction":
            self.instruction_btn.configure(style="ActiveFunction.TButton")
        elif self.current_function == "advanced":
            self.advanced_btn.configure(style="ActiveFunction.TButton")
    
    def add_separators(self):
        """添加分隔线"""
        # 在左侧面板和中间面板之间添加分隔线
        left_separator = ttk.Frame(self.main_frame, width=1, style="Separator.TFrame")
        left_separator.pack(side=tk.LEFT, fill=tk.Y, padx=1)
        
        # 在中间面板和右侧面板之间添加分隔线
        right_separator = ttk.Frame(self.main_frame, width=1, style="Separator.TFrame")
        right_separator.pack(side=tk.RIGHT, fill=tk.Y, padx=1) 
        
        """
批量配音界面模块，用于批量处理文本文件生成配音
"""
# =================== 批量配音界面模块 ===================

# ------------------------------------------------------------------
# 分割字数预设文件路径（放在项目根目录，和 parameter_presets.json 同级）
# ------------------------------------------------------------------
ROOT_DIR = Path(__file__).resolve().parent
SPLIT_PRESET_FILE = ROOT_DIR / "split_size_preset.json"

class BatchTTSFrame(ttk.Frame):
    """批量配音界面，支持文件列表和批量处理"""

    def __init__(self, parent, settings_frame):
        """
        初始化批量配音界面
        
        参数:
            parent: 父窗口
            settings_frame: 设置界面实例
        """
        super().__init__(parent)
        
        # 保存设置界面引用
        self.settings_frame = settings_frame
        
        # 初始化变量
        self.init_variables()
        
        # 创建界面组件
        self.create_widgets()
        
        # 监听设置变化
        self.settings_change_timer = None
        self.start_settings_monitoring()
        
    def init_variables(self):
        """初始化变量"""
        # 文件列表数据
        self.file_list = []
        
        # 分割字数
        self.split_size = tk.StringVar(value="50000")
        # 第一个文件分割字数
        self.first_file_size = tk.StringVar(value="50000")
        
        # 是否显示分割预览
        self.show_split_preview = tk.BooleanVar(value=True)
        
        # 处理状态
        self.is_processing = False
        self.is_paused = False
        self.current_file_index = -1
        
        # 输出目录
        self.output_dir = ""
        
        # 尝试加载已保存的分割字数预设
        self.load_split_preset()  # type: ignore[attr-defined]
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器，使用网格布局代替传统的pack布局
        self.main_container = ttk.Frame(self)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题区域
        self.create_title_area()
        
        # 工具栏区域 - 包含添加文件、分割文件等功能
        self.create_toolbar_area()
        
        # 文件列表区域 - Excel样式的表格
        self.create_file_list_area()
        
        # 控制区域 - 输出目录、开始处理按钮等
        self.create_control_area()
        
        # 状态区域 - 显示进度和状态
        self.create_status_area()
        
    def create_title_area(self):
        """创建标题区域"""
        # 标题区域容器
        title_frame = ttk.Frame(self.main_container)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 使用更现代的标题设计
        title_label = ttk.Label(
            title_frame, 
            text="批量配音管理", 
            font=("微软雅黑", 16, "bold"),
            foreground="#1565C0"
        )
        title_label.pack(side=tk.LEFT)
        
        # 文件数量统计 - 右对齐
        self.file_count_var = tk.StringVar(value="文件数量: 0")
        count_label = ttk.Label(
            title_frame, 
            textvariable=self.file_count_var,
            font=("微软雅黑", 10)
        )
        count_label.pack(side=tk.RIGHT, padx=5)
        
        # 时长计算器按钮 - 放在文件数量旁边
        calc_btn = ttk.Button(
            title_frame, 
            text="时长计算器",
            command=self.show_time_calculator,
            width=10
        )
        calc_btn.pack(side=tk.RIGHT, padx=5)
        
        # 保存分割字数预设按钮 - 紧挨时长计算器
        save_preset_btn = ttk.Button(
            title_frame,
            text="保存预设",
            command=self.save_split_preset,  # type: ignore[attr-defined]
            width=10
        )
        save_preset_btn.pack(side=tk.RIGHT, padx=5)
        
    def create_toolbar_area(self):
        """创建工具栏区域"""
        # 工具栏容器
        toolbar_frame = ttk.Frame(self.main_container)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧工具栏 - 文件操作按钮
        left_toolbar = ttk.Frame(toolbar_frame)
        left_toolbar.pack(side=tk.LEFT, fill=tk.Y)
        
        # 创建按钮样式 - 使用图标按钮+文字标签的布局
        button_style = {"width": 10, "padding": (5, 3)}
        
        # 添加文件按钮
        add_file_btn = ttk.Button(
            left_toolbar, 
            text="添加文件",
            command=self.add_files,
            **button_style
        )
        add_file_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 添加文件夹按钮
        add_folder_btn = ttk.Button(
            left_toolbar, 
            text="添加文件夹",
            command=self.add_folder,
            **button_style
        )
        add_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 清空列表按钮
        clear_btn = ttk.Button(
            left_toolbar, 
            text="清空列表",
            command=self.clear_list,
            **button_style
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 右侧工具栏 - 分割设置
        right_toolbar = ttk.Frame(toolbar_frame)
        right_toolbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 第一个文件分割字数设置
        ttk.Label(right_toolbar, text="首个文件字数:").pack(side=tk.LEFT, padx=(0, 5))
        first_file_entry = ttk.Entry(
            right_toolbar, 
            textvariable=self.first_file_size, 
            width=8,
            justify=tk.RIGHT
        )
        first_file_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # 分割字数设置
        ttk.Label(right_toolbar, text="分割字数:").pack(side=tk.LEFT, padx=(0, 5))
        
        # 使用可调节的数字输入框
        split_entry = ttk.Entry(
            right_toolbar, 
            textvariable=self.split_size, 
            width=8,
            justify=tk.RIGHT
        )
        split_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        # 显示预览选项
        preview_check = ttk.Checkbutton(
            right_toolbar,
            text="显示预览",
            variable=self.show_split_preview,
            onvalue=True,
            offvalue=False
        )
        preview_check.pack(side=tk.LEFT, padx=(5, 10))
        
        # 分割按钮
        split_btn = ttk.Button(
            right_toolbar, 
            text="分割文件",
            command=self.split_files,
            **button_style
        )
        split_btn.pack(side=tk.LEFT)
        
    def create_file_list_area(self):
        """创建文件列表区域 - Excel样式的表格"""
        # 文件列表容器 - 使用Frame增加边框效果
        list_container = ttk.Frame(self.main_container, style="TableContainer.TFrame")
        list_container.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建表格
        columns = ("name", "size", "estimated_time", "status", "progress")
        self.tree = ttk.Treeview(
            list_container, 
            columns=columns, 
            show="headings", 
            selectmode="extended"  # 允许多选
        )
        
        # 设置列宽和对齐方式
        self.tree.column("name", width=300, anchor="w")  # 增加文件名列宽度
        self.tree.column("size", width=100, anchor="e")
        self.tree.column("estimated_time", width=100, anchor="center")
        self.tree.column("status", width=100, anchor="center")
        self.tree.column("progress", width=100, anchor="center")
        
        # 设置列标题 - 更现代的标题样式
        self.tree.heading("name", text="文件名")
        self.tree.heading("size", text="大小(字)")
        self.tree.heading("estimated_time", text="预计时长")
        self.tree.heading("status", text="状态")
        self.tree.heading("progress", text="进度")
        
        # 添加垂直滚动条
        v_scrollbar = ttk.Scrollbar(list_container, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # 添加水平滚动条
        h_scrollbar = ttk.Scrollbar(list_container, orient="horizontal", command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定右键菜单和双击事件
        self.tree.bind("<Button-3>", self.show_context_menu)
        self.tree.bind("<Double-1>", lambda e: self.show_file_details())
        
    def create_control_area(self):
        """创建控制区域"""
        # 控制区域容器
        control_frame = ttk.Frame(self.main_container)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 输出目录选择区域
        output_frame = ttk.Frame(control_frame)
        output_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 使用更现代的标签和输入框设计
        ttk.Label(output_frame, text="输出目录:", font=("微软雅黑", 10)).pack(side=tk.LEFT, padx=(0, 5))
        
        # 创建一个带边框的输出路径显示区域
        path_container = ttk.Frame(output_frame, style="PathContainer.TFrame")
        path_container.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        self.output_path_var = tk.StringVar(value="")
        output_path_label = ttk.Label(
            path_container, 
            textvariable=self.output_path_var,
            background="#FFFFFF",
            anchor="w",
            padding=(5, 2)
        )
        output_path_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 选择目录按钮
        output_btn = ttk.Button(
            output_frame, 
            text="浏览...",
            command=self.select_output_dir,
            width=8
        )
        output_btn.pack(side=tk.LEFT)
        
        # 处理按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.RIGHT)
        
        # 使用更现代的按钮设计
        button_style = {"width": 8, "padding": (5, 3)}
        
        # 开始处理按钮
        self.start_btn = ttk.Button(
            button_frame, 
            text="开始处理",
            command=self.start_batch,
            **button_style
        )
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 暂停按钮
        self.pause_btn = ttk.Button(
            button_frame, 
            text="暂停",
            command=self.pause_batch,
            state="disabled",
            **button_style
        )
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 停止按钮
        self.stop_btn = ttk.Button(
            button_frame, 
            text="停止",
            command=self.stop_batch,
            state="disabled",
            **button_style
        )
        self.stop_btn.pack(side=tk.LEFT)
        
    def create_status_area(self):
        """创建状态显示区域"""
        # 状态区域容器
        status_frame = ttk.Frame(self.main_container)
        status_frame.pack(fill=tk.X)
        
        # 状态标签
        ttk.Label(status_frame, text="总进度:").pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条容器
        progress_container = ttk.Frame(status_frame)
        progress_container.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 进度条
        self.total_progress_var = tk.DoubleVar()
        self.total_progress_bar = ttk.Progressbar(
            progress_container, 
            variable=self.total_progress_var, 
            maximum=100,
            mode='determinate',
            length=200
        )
        self.total_progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # 进度百分比
        self.progress_percent_var = tk.StringVar(value="0%")
        percent_label = ttk.Label(
            status_frame, 
            textvariable=self.progress_percent_var, 
            width=5
        )
        percent_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态文本
        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(
            status_frame, 
            textvariable=self.status_var,
            font=("微软雅黑", 9)
        )
        status_label.pack(side=tk.RIGHT)
        
    def show_file_details(self):
        """显示选中文件的详细信息"""
        selected_item = self.tree.focus()
        if not selected_item:
            return
            
        # 获取文件信息
        values = self.tree.item(selected_item, "values")
        if values:
            file_name = values[0]
            file_size = values[1]
            
            # 查找完整文件路径
            path = None
            file_obj = None
            for file in self.file_list:
                if file["id"] == selected_item:
                    path = file["path"]
                    file_obj = file
                    break
            
            if not path:
                return
            
            # 重新计算最新的预计时长
            try:
                file_size_int = int(file_size.replace(',', ''))
                estimated_time = self.calculate_estimated_time(file_size_int)
                
                # 更新文件对象中的预计时长
                if file_obj:
                    file_obj["estimated_time"] = estimated_time
                    
                    # 更新树视图中的预计时长
                    values_list = list(values)
                    values_list[2] = estimated_time
                    self.tree.item(selected_item, values=values_list)
            except:
                estimated_time = values[2]  # 如果计算失败，使用原有值
                
            # 显示详细信息
            details = f"文件: {file_name}\n路径: {path}\n大小: {file_size} 字\n预计时长: {estimated_time}"
            messagebox.showinfo("文件详情", details)
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        # 获取选中的项
        selected_item = self.tree.identify_row(event.y)
        if not selected_item:
            return
            
        # 创建右键菜单
        context_menu = tk.Menu(self, tearoff=0)
        context_menu.add_command(label="删除", command=lambda: self.remove_item(selected_item))
        context_menu.add_command(label="查看路径", command=lambda: self.show_path(selected_item))
        
        # 显示菜单
        context_menu.post(event.x_root, event.y_root)
        
    def add_files(self):
        """添加文件到列表"""
        # 如果正在处理，不允许添加文件
        if self.is_processing:
            messagebox.showinfo("提示", "当前正在处理中，无法添加文件")
            return
            
        # 打开文件选择对话框
        file_paths = filedialog.askopenfilenames(
            title="选择文本文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if not file_paths:
            return
            
        # 更新状态
        self.status_var.set("正在添加文件...")
        
        # 创建线程处理文件添加
        threading.Thread(target=self._add_files_thread, args=(file_paths,), daemon=True).start()
        
    def _add_files_thread(self, file_paths):
        """在线程中处理文件添加"""
        try:
            # 创建文本处理器
            text_processor = TextProcessor()
            
            # 添加文件
            total_files = len(file_paths)
            added_files = 0
            
            # 批量预处理，减少UI更新次数
            batch_size = 20  # 增加每批处理的文件数，从5增加到20
            current_batch = []
            
            for file_path in file_paths:
                # 检查文件是否已存在于列表中
                if self._is_file_in_list(file_path):
                    continue
                    
                try:
                    # 获取文件名
                    file_name = os.path.basename(file_path)
                    
                    # 获取文件大小 - 优化：不用完整读取文件内容来计算大小
                    try:
                        # 使用更高效的方法获取文件字符数
                        file_size = os.path.getsize(file_path)  # 先获取字节大小
                        # 抽样检查若干行来估算平均每字节包含的字符数
                        sample_size = min(10 * 1024, file_size)  # 最多取10KB样本
                        if sample_size > 0:
                            with open(file_path, 'rb') as f:
                                sample = f.read(sample_size)
                                # 尝试检测编码
                                encoding = chardet.detect(sample)['encoding'] or 'utf-8'
                                # 将样本解码为文本并计算字符数
                                sample_text = sample.decode(encoding, errors='replace')
                                # 估算整个文件的字符数
                                char_byte_ratio = len(sample_text) / sample_size
                                file_size = int(file_size * char_byte_ratio)
                    except Exception:
                        # 如果估算失败，使用文件大小（字节）作为粗略估计
                        file_size = os.path.getsize(file_path)
                    
                    # 添加到当前批次
                    current_batch.append({
                        "name": file_name,
                        "path": file_path,
                        "size": file_size
                    })
                    
                    # 如果当前批次已满或是最后一个文件，更新UI
                    if len(current_batch) >= batch_size or file_path == file_paths[-1]:
                        # 更新UI - 使用after避免界面卡顿
                        self.master.after(0, lambda batch=current_batch.copy(): self._add_batch_to_tree(batch))
                        current_batch = []
                        
                    # 更新进度
                    added_files += 1
                    progress = added_files / total_files * 100
                    
                    # 降低进度更新频率，减少UI更新
                    if added_files % 10 == 0 or added_files == total_files:  # 从5改为10
                        self.master.after(0, lambda p=progress: self.total_progress_var.set(p))
                        self.master.after(0, lambda p=progress: self.progress_percent_var.set(f"{int(p)}%"))
                        self.master.after(0, lambda a=added_files, t=total_files: 
                                      self.status_var.set(f"正在添加文件... {a}/{t}"))
                        
                except Exception as e:
                    print(f"添加文件 {file_path} 失败: {str(e)}")
            
            # 更新文件数量
            self.master.after(100, lambda: self.file_count_var.set(f"文件数量: {len(self.file_list)}"))
            
            # 更新状态
            self.master.after(100, lambda: self.status_var.set(f"已添加 {added_files} 个文件"))
            self.master.after(100, lambda: self.total_progress_var.set(0))
            self.master.after(100, lambda: self.progress_percent_var.set("0%"))
            
        except Exception as e:
            print(f"添加文件失败: {str(e)}")
            self.master.after(0, lambda err=str(e): 
                          messagebox.showerror("添加失败", f"添加文件失败: {err}"))
            self.master.after(0, lambda: self.status_var.set("添加失败"))
            
    def _add_batch_to_tree(self, batch):
        """将一批文件添加到树状图和文件列表"""
        for file in batch:
            # 计算预计时长
            estimated_time = self.calculate_estimated_time(file['size'])
            
            item_id = self.tree.insert("", "end", values=(
                file["name"],
                f"{file['size']}",
                estimated_time,
                "待处理",
                "0%"
            ))
            
            self.file_list.append({
                "id": item_id,
                "name": file["name"],
                "path": file["path"],
                "size": file["size"],
                "estimated_time": estimated_time,
                "status": "待处理",
                "progress": 0
            })
        
    def add_folder(self):
        """添加文件夹中的所有文本文件到列表"""
        # 如果正在处理，不允许添加
        if self.is_processing:
            messagebox.showinfo("提示", "当前正在处理中，无法添加文件")
            return
            
        # 打开文件夹选择对话框
        folder_path = filedialog.askdirectory(title="选择包含文本文件的文件夹")
        
        if not folder_path:
            return
            
        # 更新状态
        self.status_var.set("正在扫描文件夹...")
        
        # 创建线程处理文件夹添加
        threading.Thread(target=self._add_folder_thread, args=(folder_path,), daemon=True).start()
        
    def _add_folder_thread(self, folder_path):
        """在线程中处理文件夹添加"""
        try:
            # 创建文本处理器
            text_processor = TextProcessor()
            
            # 获取所有文本文件
            all_files = []
            for root, _, files in os.walk(folder_path):
                for file in files:
                    # 只处理.txt文件
                    if file.endswith('.txt'):
                        file_path = os.path.join(root, file)
                        all_files.append(file_path)
            
            if not all_files:
                self.master.after(0, lambda: messagebox.showinfo("提示", "文件夹中没有找到文本文件"))
                self.master.after(0, lambda: self.status_var.set("未添加任何文件"))
                return
            
            # 更新状态
            total_files = len(all_files)
            self.master.after(0, lambda: self.status_var.set(f"发现 {total_files} 个文本文件，正在添加..."))
            
            # 添加文件
            added_files = 0
            
            # 批量预处理，减少UI更新次数
            batch_size = 20  # 从5增加到20
            current_batch = []
            
            for file_path in all_files:
                # 检查文件是否已存在于列表中
                if self._is_file_in_list(file_path):
                    continue
                    
                try:
                    # 获取文件名
                    file_name = os.path.basename(file_path)
                    
                    # 获取文件大小 - 优化：与上面相同的优化
                    try:
                        # 使用更高效的方法获取文件字符数
                        file_size = os.path.getsize(file_path)  # 先获取字节大小
                        # 抽样检查若干行来估算平均每字节包含的字符数
                        sample_size = min(10 * 1024, file_size)  # 最多取10KB样本
                        if sample_size > 0:
                            with open(file_path, 'rb') as f:
                                sample = f.read(sample_size)
                                # 尝试检测编码
                                encoding = chardet.detect(sample)['encoding'] or 'utf-8'
                                # 将样本解码为文本并计算字符数
                                sample_text = sample.decode(encoding, errors='replace')
                                # 估算整个文件的字符数
                                char_byte_ratio = len(sample_text) / sample_size
                                file_size = int(file_size * char_byte_ratio)
                    except Exception:
                        # 如果估算失败，使用文件大小（字节）作为粗略估计
                        file_size = os.path.getsize(file_path)
                    
                    # 添加到当前批次
                    current_batch.append({
                        "name": file_name,
                        "path": file_path,
                        "size": file_size
                    })
                    
                    # 如果当前批次已满或是最后一个文件，更新UI
                    if len(current_batch) >= batch_size or file_path == all_files[-1]:
                        # 更新UI - 使用after避免界面卡顿
                        self.master.after(0, lambda batch=current_batch.copy(): self._add_batch_to_tree(batch))
                        current_batch = []
                        
                    # 更新进度
                    added_files += 1
                    progress = added_files / total_files * 100
                    
                    # 降低进度更新频率，减少UI更新
                    if added_files % 10 == 0 or added_files == total_files:  # 从5改为10
                        self.master.after(0, lambda p=progress: self.total_progress_var.set(p))
                        self.master.after(0, lambda p=progress: self.progress_percent_var.set(f"{int(p)}%"))
                        self.master.after(0, lambda a=added_files, t=total_files: 
                                      self.status_var.set(f"正在添加文件... {a}/{t}"))
                        
                except Exception as e:
                    print(f"添加文件 {file_path} 失败: {str(e)}")
            
            # 更新文件数量
            self.master.after(100, lambda: self.file_count_var.set(f"文件数量: {len(self.file_list)}"))
            
            # 更新状态
            self.master.after(100, lambda: self.status_var.set(f"已从文件夹添加 {added_files} 个文件"))
            self.master.after(100, lambda: self.total_progress_var.set(0))
            self.master.after(100, lambda: self.progress_percent_var.set("0%"))
            
        except Exception as e:
            print(f"添加文件夹失败: {str(e)}")
            self.master.after(0, lambda err=str(e): 
                          messagebox.showerror("添加失败", f"添加文件夹失败: {err}"))
            self.master.after(0, lambda: self.status_var.set("添加失败"))
        
    def remove_item(self, item_id):
        """从列表中删除选中项"""
        # 如果正在处理，不允许删除
        if self.is_processing:
            messagebox.showinfo("提示", "当前正在处理中，无法删除文件")
            return
            
        # 从数据列表中删除
        for i, item in enumerate(self.file_list):
            if item["id"] == item_id:
                self.file_list.pop(i)
                break
                
        # 从树状视图中删除
        self.tree.delete(item_id)
        
        # 更新文件数量
        self.file_count_var.set(f"文件数量: {len(self.file_list)}")
        
        # 更新状态
        self.status_var.set("已删除文件")
        
    def show_path(self, item_id):
        """显示文件完整路径"""
        # 从文件列表中获取完整路径
        path = None
        for file in self.file_list:
            if file["id"] == item_id:
                path = file["path"]
                break
                
        if path:
            messagebox.showinfo("文件路径", path)
            
    def clear_list(self):
        """清空文件列表"""
        # 如果正在处理，不允许清空
        if self.is_processing:
            messagebox.showinfo("提示", "当前正在处理中，无法清空列表")
            return
            
        # 确认框
        if messagebox.askyesno("确认清空", "确定要清空文件列表吗？"):
            # 清空树状视图
            for item in self.tree.get_children():
                self.tree.delete(item)
                
            # 清空数据列表
            self.file_list = []
            
            # 更新文件数量
            self.file_count_var.set("文件数量: 0")
            
            # 更新状态
            self.status_var.set("已清空文件列表")
            
    def split_files(self):
        """根据字数拆分文件"""
        # 如果正在处理，不允许拆分
        if self.is_processing:
            messagebox.showinfo("提示", "当前正在处理中，无法拆分文件")
            return
            
        # 检查是否有文件
        if not self.file_list:
            messagebox.showinfo("提示", "请先添加文件")
            return
            
        # 获取分割字数
        try:
            split_size = int(self.split_size.get())
            first_file_size = int(self.first_file_size.get())
            if split_size <= 0 or first_file_size <= 0:
                raise ValueError("分割字数必须大于0")
        except ValueError as e:
            messagebox.showerror("错误", f"分割字数格式错误: {str(e)}")
            return
            
        # 确认框
        if not messagebox.askyesno("确认分割", f"确定要按照首个文件 {first_file_size} 字，其余 {split_size} 字分割文件吗？"):
            return
            
        # 更新预计时长，确保使用最新设置
        self.update_estimated_times()
            
        # 更新状态
        self.status_var.set("正在分割文件...")
        
        # 创建线程处理分割
        threading.Thread(target=self._split_files_thread, args=(split_size, first_file_size), daemon=True).start()
        
    def _split_files_thread(self, split_size, first_file_size=None):
        """在线程中处理文件分割"""
        try:
            # 如果没有指定首个文件大小，使用通用分割大小
            if first_file_size is None:
                first_file_size = split_size
                
            # 原始文件列表
            original_files = self.file_list.copy()
            
            # 创建新文件列表（不立即清空当前列表）
            new_files = []
            
            # 创建文本处理器
            text_processor = TextProcessor()
            total_files = len(original_files)
            processed_files = 0
            
            # 更新UI，表明分割开始
            self.master.after(0, lambda: self.status_var.set(f"准备分割 {total_files} 个文件..."))
            
            # 第一阶段：分析文件
            self.master.after(0, lambda: self.status_var.set(f"阶段1/3: 正在分析文件..."))
            
            # 存储分割信息，用于预览和实际分割
            split_info = []
            
            for file in original_files:
                # 更新UI进度 - 第一阶段占30%
                processed_files += 1
                progress = (processed_files / total_files) * 30
                self.master.after(0, lambda p=progress: self.total_progress_var.set(p))
                self.master.after(0, lambda p=progress: self.progress_percent_var.set(f"{int(p)}%"))
                self.master.after(0, lambda f=file, pf=processed_files, tf=total_files: 
                               self.status_var.set(f"阶段1/3: 分析文件 {f['name']} ({pf}/{tf})"))
                
                # 分析文件
                try:
                    # 获取文件内容
                    try:
                        content = text_processor.load_text_file(file["path"])
                    except UnicodeDecodeError:
                        # 如果编码检测失败，尝试直接使用UTF-8
                        with open(file["path"], 'r', encoding='utf-8', errors='replace') as f:
                            content = f.read()
                    
                    # 获取文件大小
                    file_size = len(content)
                    
                    # 如果文件大小小于分割字数，保持不变
                    if file_size <= first_file_size:
                        split_info.append({
                            "original_file": file,
                            "needs_split": False,
                            "content": content,
                            "parts": []
                        })
                        continue
                    
                    # 严格控制分割大小，确保不会超过设定值
                    remaining_text = content
                    split_parts = []
                    
                    # 首先切出第一部分（使用first_file_size）
                    if len(remaining_text) > 0:
                        # 确保不超过文本总长度
                        actual_size = min(first_file_size, len(remaining_text))
                        # 查找最近的句子结束位置
                        end_pos = text_processor.find_sentence_end(remaining_text, actual_size)
                        # 确保端点位置不超过设置的最大字数
                        if end_pos > first_file_size * 1.05:  # 允许5%的误差
                            # 如果没有合适的断句点，强制在最大字数处截断
                            end_pos = first_file_size
                        
                        first_part = remaining_text[:end_pos]
                        remaining_text = remaining_text[end_pos:]
                        split_parts.append(first_part)
                    
                    # 处理剩余部分（使用split_size）
                    while len(remaining_text) > 0:
                        # 确保不超过文本总长度
                        actual_size = min(split_size, len(remaining_text))
                        if actual_size <= 0:
                            break
                            
                        # 查找最近的句子结束位置
                        end_pos = text_processor.find_sentence_end(remaining_text, actual_size)
                        # 确保端点位置不超过设置的最大字数
                        if end_pos > split_size * 1.05:  # 允许5%的误差
                            # 如果没有合适的断句点，强制在最大字数处截断
                            end_pos = split_size
                            
                        part = remaining_text[:end_pos]
                        remaining_text = remaining_text[end_pos:]
                        
                        # 检查是否还有文本
                        if len(part) > 0:
                            split_parts.append(part)
                        else:
                            break
                    
                    # 将分割信息添加到列表
                    part_info = []
                    for i, part in enumerate(split_parts):
                        # 生成分割文件名
                        base_name, ext = os.path.splitext(file["name"])
                        split_name = f"{base_name}-{i+1}{ext}"
                        
                        part_info.append({
                            "name": split_name,
                            "size": len(part),
                            "part": i+1,
                            "total_parts": len(split_parts)
                        })
                    
                    split_info.append({
                        "original_file": file,
                        "needs_split": True,
                        "content": content,
                        "parts": part_info
                    })
                    
                except Exception as e:
                    print(f"分析文件 {file['name']} 失败: {str(e)}")
                    self.master.after(0, lambda name=file["name"], err=str(e): 
                                    messagebox.showerror("分析失败", f"分析文件 {name} 失败: {err}"))
            
            # 第二阶段：显示预览并确认
            self.master.after(0, lambda: self.status_var.set("阶段2/3: 显示分割预览..."))
            self.master.after(0, lambda: self.total_progress_var.set(40))
            self.master.after(0, lambda: self.progress_percent_var.set("40%"))
            
            # 需要分割的文件数量
            files_to_split = sum(1 for info in split_info if info["needs_split"])
            total_parts = sum(len(info["parts"]) for info in split_info if info["needs_split"])
            
            # 如果没有文件需要分割，显示提示
            if files_to_split == 0:
                self.master.after(0, lambda: messagebox.showinfo("分割预览", "所有文件都小于分割大小，无需分割。"))
                self.master.after(0, lambda: self.status_var.set("分割已取消"))
                self.master.after(0, lambda: self.total_progress_var.set(0))
                self.master.after(0, lambda: self.progress_percent_var.set("0%"))
                return
            
            # 如果用户设置了显示预览
            if self.show_split_preview.get():
                # 创建分割预览信息
                preview_text = f"即将分割 {files_to_split} 个文件，生成 {total_parts} 个片段。\n\n"
                preview_text += "分割预览:\n"
                preview_text += "------------------------------------\n"
                
                max_filename_display_len = 35 # 文件名最大显示长度

                for info in split_info:
                    if info["needs_split"]:
                        original_name = info["original_file"]["name"]
                        original_size = info["original_file"]["size"]
                        
                        display_original_name = original_name
                        if len(original_name) > max_filename_display_len:
                            display_original_name = original_name[:max_filename_display_len-3] + "..."

                        preview_text += f"原文件: {display_original_name} ({original_size}字)\n"
                        
                        for part_idx, part in enumerate(info["parts"]):
                            part_name = part['name']
                            part_size = part['size']
                            
                            display_part_name = part_name
                            if len(part_name) > max_filename_display_len:
                                display_part_name = part_name[:max_filename_display_len-3] + "..."

                            # 计算预计时长
                            estimated_part_time = self.calculate_estimated_time(part_size)
                            preview_text += f"  → 片段{part_idx + 1}: {display_part_name} ({part_size}字, 预计: {estimated_part_time})\n"
                        preview_text += "------------------------------------\n"

                # 使用全局变量存储结果
                self.confirm_dialog_result = None
                
                # 使用更可靠的方式等待对话框结果
                def show_confirm_dialog():
                    self.confirm_dialog_result = messagebox.askyesno("分割预览", preview_text + "\n是否继续分割?")
                
                self.master.after(0, show_confirm_dialog)
                
                # 等待确认结果
                while self.confirm_dialog_result is None:
                    time.sleep(0.1)
                    # 处理Tkinter事件，避免界面卡死
                    try:
                        self.master.update()
                    except:
                        pass
                
                # 如果用户取消，退出处理
                if not self.confirm_dialog_result:
                    self.master.after(0, lambda: self.status_var.set("分割已取消"))
                    self.master.after(0, lambda: self.total_progress_var.set(0))
                    self.master.after(0, lambda: self.progress_percent_var.set("0%"))
                    return
            
            # 第三阶段：执行实际分割
            self.master.after(0, lambda: self.status_var.set("阶段3/3: 执行分割操作..."))
            self.master.after(0, lambda: self.total_progress_var.set(50))
            self.master.after(0, lambda: self.progress_percent_var.set("50%"))
            
            processed_files = 0
            
            for info in split_info:
                processed_files += 1
                
                if not info["needs_split"]:
                    # 如果文件不需要分割，直接添加到新列表
                    new_files.append({
                        "name": info["original_file"]["name"],
                        "path": info["original_file"]["path"],
                        "size": len(info["content"])
                    })
                    continue
                
                # 获取原始文件信息
                file = info["original_file"]
                
                # 为分割文件创建目录
                output_dir = os.path.join(os.path.dirname(file["path"]), "splits")
                os.makedirs(output_dir, exist_ok=True)
                
                # 分割文件
                file_split_parts = []
                for i, part_info in enumerate(info["parts"]):
                    # 生成分割文件路径
                    split_path = os.path.join(output_dir, part_info["name"])
                    
                    # 获取当前部分的文本内容 - 直接使用预先分析好的大小确保一致性
                    part_text = None
                    remaining = info["content"]
                    
                    # 提取相应的部分文本
                    for j in range(i+1):
                        if j == i:  # 当前需要的部分
                            part_size = part_info["size"]
                            # 确保不超出剩余文本长度
                            if len(remaining) >= part_size:
                                part_text = remaining[:part_size]
                            else:
                                part_text = remaining
                            break
                        else:  # 跳过之前的部分
                            prev_part_size = info["parts"][j]["size"]
                            if len(remaining) > prev_part_size:
                                remaining = remaining[prev_part_size:]
                            else:
                                # 如果出现意外情况，使用剩余全部文本
                                part_text = remaining
                                break
                    
                    # 检查是否成功提取文本内容
                    if part_text is None or len(part_text) == 0:
                        print(f"警告: 无法提取 {part_info['name']} 的内容")
                        continue
                    
                    # 写入分割文件
                    try:
                        with open(split_path, 'w', encoding='utf-8') as f:
                            f.write(part_text)
                    except UnicodeEncodeError:
                        # 如果UTF-8编码失败，尝试使用系统默认编码
                        with open(split_path, 'w', encoding=locale.getpreferredencoding(), errors='replace') as f:
                            f.write(part_text)
                    
                    # 添加到分割文件列表
                    file_split_parts.append({
                        "name": part_info["name"],
                        "path": split_path,
                        "size": len(part_text)
                    })
                    
                    # 更新进度 - 第三阶段从50%到100%
                    total_parts_to_process = sum(len(s["parts"]) for s in split_info if s["needs_split"])
                    parts_processed = sum(len(s["parts"]) for s in split_info[:split_info.index(info)] if s["needs_split"]) + i + 1
                    progress = 50 + (parts_processed / total_parts_to_process) * 50
                    
                    self.master.after(0, lambda p=progress: self.total_progress_var.set(p))
                    self.master.after(0, lambda p=progress: self.progress_percent_var.set(f"{int(p)}%"))
                    self.master.after(0, lambda f=file["name"], pi=i+1, pt=len(info["parts"]): 
                                   self.status_var.set(f"阶段3/3: 分割文件 {f} ({pi}/{pt})"))
                
                # 如果成功创建了分割文件，标记原文件需要删除
                if file_split_parts:
                    file["to_remove"] = True
                    # 添加分割文件到新文件列表
                    new_files.extend(file_split_parts)
            
            # 确保进度条显示100%
            self.master.after(0, lambda: self.total_progress_var.set(100))
            self.master.after(0, lambda: self.progress_percent_var.set("100%"))
            
            # 更新UI，添加新分割的文件
            if new_files:
                # 批量添加新文件到列表，每20个一批
                batch_size = 20
                current_new_batch = []
                
                for i, new_file in enumerate(new_files):
                    current_new_batch.append(new_file)
                    
                    # 如果当前批次已满或是最后一个文件，更新UI
                    if len(current_new_batch) >= batch_size or i == len(new_files) - 1:
                        # 更新UI - 使用after避免界面卡顿
                        self.master.after(0, lambda batch=current_new_batch.copy(): 
                                       self._add_batch_to_tree(batch))
                        current_new_batch = []
                
                # 删除已被分割的原文件
                items_to_remove = []
                for file in original_files:
                    if file.get("to_remove", False):
                        items_to_remove.append(file["id"])
                
                # 更新UI，从列表中删除原文件
                if items_to_remove:
                    self.master.after(0, lambda items=items_to_remove: [self.tree.delete(item_id) for item_id in items])
                    
                    # 从文件列表中删除被分割的原文件
                    self.file_list = [file for file in self.file_list if file.get("id") not in items_to_remove]
            
            # 更新文件数量 - 延迟执行避免UI卡顿
            self.master.after(100, lambda: self.file_count_var.set(f"文件数量: {len(self.file_list)}"))
            
            # 更新状态
            self.master.after(100, lambda: self.status_var.set(f"已完成分割，共 {len(self.file_list)} 个文件"))
            self.master.after(100, lambda: self.total_progress_var.set(0))
            self.master.after(100, lambda: self.progress_percent_var.set("0%"))
            
        except Exception as e:
            print(f"分割文件失败: {str(e)}")
            self.master.after(0, lambda err=str(e): 
                          messagebox.showerror("分割失败", f"分割文件失败: {err}"))
            self.master.after(0, lambda: self.status_var.set("分割失败"))
            
    def select_output_dir(self):
        """选择输出目录"""
        # 打开文件夹选择对话框
        output_dir = filedialog.askdirectory(title="选择输出目录")
        
        if output_dir:
            self.output_dir = output_dir
            self.output_path_var.set(output_dir)
            
    def start_batch(self):
        """开始批处理"""
        # 检查是否有文件
        if len(self.file_list) == 0:
            messagebox.showinfo("提示", "请先添加文件")
            return
            
        # 检查是否已经在处理
        if self.is_processing:
            return
            
        # 检查是否选择了输出目录
        if not self.output_dir:
            messagebox.showinfo("提示", "请先选择输出目录")
            return
            
        # 检查是否有上次未完成的处理
        resume = False
        if self.check_previous_state():
            resume = True
        else:
            # 重置处理状态
            self.current_file_index = -1
        
        # 更新状态
        self.is_processing = True
        
        # 更新按钮状态
        self.start_btn.configure(state="disabled")
        self.pause_btn.configure(state="normal")
        self.stop_btn.configure(state="normal")
        
        # 更新状态显示
        self.status_var.set("开始处理...")
        
        # 获取设置
        settings = self.settings_frame.get_settings()
        
        # 创建并启动处理线程
        process_thread = threading.Thread(
            target=self._batch_process_thread,
            args=(settings, resume),
            daemon=True
        )
        process_thread.start()
    
    def pause_batch(self):
        """暂停批处理"""
        if self.is_processing and not self.is_paused:
            self.is_paused = True
            
            # 更新按钮状态
            self.pause_btn.configure(text="继续")
            
            # 更新状态显示
            if self.current_file_index >= 0 and self.current_file_index < len(self.tree.get_children()):
                item_id = self.tree.get_children()[self.current_file_index]
                values = list(self.tree.item(item_id)["values"])
                values[-2] = "已暂停"
                self.tree.item(item_id, values=values)
            
            # 保存当前处理文件的索引和进度
            batch_processor = getattr(self, "_batch_processor", None)
            if batch_processor:
                batch_processor.current_state["file_index"] = self.current_file_index
                batch_processor.pause()
                
            self.status_var.set("处理已暂停")
        elif self.is_processing and self.is_paused:
            self.is_paused = False
            
            # 更新按钮状态
            self.pause_btn.configure(text="暂停")
            
            # 更新状态显示
            if self.current_file_index >= 0 and self.current_file_index < len(self.tree.get_children()):
                item_id = self.tree.get_children()[self.current_file_index]
                values = list(self.tree.item(item_id)["values"])
                values[-2] = "处理中"
                self.tree.item(item_id, values=values)
            
            # 恢复处理
            batch_processor = getattr(self, "_batch_processor", None)
            if batch_processor:
                batch_processor.resume()
                
            self.status_var.set("正在处理...")
    
    def check_previous_state(self):
        """检查是否有上次未完成的处理"""
        # 创建批处理器
        self._batch_processor = BatchProcessor(self.settings_frame.get_settings())
        
        # 不再支持状态保存和恢复功能
        return False
        
    def _batch_process_thread(self, settings, resume=False):
        """批处理线程"""
        try:
            # 创建批处理器
            batch_processor = BatchProcessor(settings)
            self._batch_processor = batch_processor
            
            # 定义界面更新函数
            def update_tree_progress(index, progress, status):
                """更新树状视图中的进度"""
                # 获取所有条目
                items = self.tree.get_children()
                
                # 确保索引有效
                if 0 <= index < len(items):
                    item_id = items[index]
                    
                    # 获取当前值
                    values = list(self.tree.item(item_id)["values"])
                    
                    # 更新状态和进度
                    values[-2] = status
                    values[-1] = f"{int(progress)}%"  # 确保进度为整数
                    
                    # 更新树状视图，通过after方法保证线程安全
                    self.master.after(0, lambda: self.tree.item(item_id, values=values))
                    
                    # 同时更新数据模型
                    if index < len(self.file_list):
                        self.file_list[index]["status"] = status
                        self.file_list[index]["progress"] = int(progress)  # 确保进度为整数
                        
                # 更新当前处理的文件索引
                self.current_file_index = index
                
                # 更新全局进度
                if len(items) > 0:
                    global_progress = (index * 100 + progress) / len(items)
                    self.master.after(0, lambda p=global_progress: self.total_progress_var.set(int(p)))  # 确保进度为整数
                    self.master.after(0, lambda p=global_progress: 
                                   self.progress_percent_var.set(f"{int(p)}%"))  # 确保进度为整数
            
            # 始终从第一个文件开始处理
            start_index = 0
                
            # 如果没有文件，直接返回
            if not self.file_list:
                self.master.after(0, lambda: self.status_var.set("无文件需要处理"))
                self.master.after(0, self._processing_finished)
                return
                
            # 获取文件列表
            file_items = self.file_list
            
            # 更新状态
            self.master.after(0, lambda: self.status_var.set(f"处理中: {start_index+1}/{len(file_items)}"))
            
            # 定义更新进度的函数
            def update_progress(value, status=""):
                # 检查是否暂停
                while batch_processor.pause_flag and not batch_processor.stop_flag:
                    time.sleep(0.5)
                
                # 检查是否停止
                if batch_processor.stop_flag:
                    # 停止处理
                    self.master.after(0, lambda: self.status_var.set("处理已停止"))
                    self.master.after(0, self._processing_finished)
                    return
                
                # 获取当前处理的文件索引
                current_index = self.current_file_index
                if current_index >= 0:
                    # 更新树状视图进度
                    update_tree_progress(current_index, value, status if status else "处理中")
                
                # 更新状态栏
                if status:
                    self.master.after(0, lambda s=status: self.status_var.set(s))
            
            # 使用新的批处理方法按照开发文档的流程处理文件
            successful_files = batch_processor.process_files_by_batches(
                file_items[start_index:],
                self.output_dir,
                update_progress,
                lambda index: update_tree_progress(start_index + index, 0, "处理中")
            )
            
            # 检查是否处理成功
            if successful_files > 0:
                # 完成处理
                self.master.after(0, lambda: self.status_var.set(
                    f"处理完成，成功: {successful_files}/{len(file_items[start_index:])}"
                ))
            else:
                # 处理失败
                self.master.after(0, lambda: self.status_var.set("处理失败"))
            
            # 处理完成
            self.master.after(0, self._processing_finished)
            
        except Exception as e:
            print(f"批处理失败: {str(e)}")
            self.master.after(0, lambda err=str(e): 
                          messagebox.showerror("处理失败", f"批处理失败: {err}"))
            self.master.after(0, lambda: self.status_var.set("处理失败"))
            self.master.after(0, self._processing_finished)
            
    def _processing_finished(self):
        """处理完成后的UI更新"""
        # 更新状态
        self.is_processing = False
        self.is_paused = False
        
        # 更新按钮状态
        self.start_btn.configure(state="normal")
        self.pause_btn.configure(state="disabled")
        self.stop_btn.configure(state="disabled")
        
        # 更新状态显示
        self.status_var.set("处理完成")
        
        # 提示用户
        messagebox.showinfo("处理完成", "所有文件处理已完成")
        
    def _is_file_in_list(self, file_path):
        """检查文件是否已存在于列表中"""
        for file in self.file_list:
            if file["path"] == file_path:
                return True
        return False

    def stop_batch(self):
        """停止处理"""
        if not self.is_processing:
            return
            
        # 确认框
        if messagebox.askyesno("确认停止", "确定要停止当前处理吗？"):
            # 更新状态
            self.is_processing = False
            self.is_paused = False
            
            # 通知批处理器停止
            if hasattr(self, "_batch_processor"):
                self._batch_processor.stop()
            
            # 更新状态显示
            self.status_var.set("已停止处理")
            
            # 更新按钮状态
            self.start_btn.configure(state="normal")
            self.pause_btn.configure(state="disabled")
            self.stop_btn.configure(state="disabled")

    def calculate_estimated_time(self, text_size, settings=None):
        """
        计算指定字数和设置下的预计时长
        
        参数:
            text_size: 文本字数
            settings: 设置参数，如果为None则获取当前设置
            
        返回:
            预计时长字符串
        """
        try:
            # 获取设置
            if settings is None:
                settings = self.settings_frame.get_settings()
                
            # 获取语速设置
            speech_rate = settings.get("speed", 1.0)  # 默认值为1.0
            
            # 基础处理速度：根据不同语速计算每分钟处理的字符数
            # 正常语速(1.0)下，中文大约每分钟300-350字，英文每分钟约150-180词
            # 考虑到大多数场景是中文，但有少量英文，取平均值
            base_rate = 320  # 正常语速每分钟处理的字符数
            
            # 语速系数调整：语速越快，处理效率略有下降，不是完全线性关系
            if speech_rate > 1.0:
                # 高速语音会有非线性效应，速度提升会逐渐降低
                rate_factor = 1.0 + (speech_rate - 1.0) * 0.8
            else:
                # 低速语音也有非线性效应，速度下降会逐渐缓和
                rate_factor = 1.0 - (1.0 - speech_rate) * 0.9
            
            # 考虑文本大小的复杂度因素：文本越长，效率可能略有下降
            size_factor = 1.0
            if text_size > 10000:
                size_factor = 1.0 + min(0.1, (text_size - 10000) / 100000)  # 最多增加10%处理时间
            
            # 考虑文本处理和音频生成的固定开销
            fixed_overhead_seconds = 5  # 每个文件的固定处理开销(秒)
            
            # 计算实际的字符处理速率
            effective_rate = base_rate * rate_factor / size_factor
            
            # 计算总时间(分钟)
            processing_minutes = text_size / effective_rate
            total_seconds = processing_minutes * 60 + fixed_overhead_seconds
            
            # 转换为时分秒格式
            hours = int(total_seconds / 3600)
            remaining_seconds = total_seconds % 3600
            minutes = int(remaining_seconds / 60)
            seconds = int(remaining_seconds % 60)
            
            # 格式化显示
            if hours > 0:
                return f"{hours}小时{minutes}分{seconds}秒"
            elif minutes > 0:
                return f"{minutes}分{seconds}秒"
            else:
                return f"{seconds}秒"
                
        except Exception as e:
            print(f"计算预计时长出错: {str(e)}")
            return "计算错误"

    def update_estimated_times(self):
        """更新所有文件的预计时长"""
        # 获取当前设置
        settings = self.settings_frame.get_settings()
        
        # 更新所有文件的预计时长
        for file in self.file_list:
            file_id = file["id"]
            file_size = file["size"]
            
            # 计算新的预计时长
            estimated_time = self.calculate_estimated_time(file_size, settings)
            
            # 更新文件对象
            file["estimated_time"] = estimated_time
            
            # 更新树视图
            values = list(self.tree.item(file_id, "values"))
            values[2] = estimated_time
            self.tree.item(file_id, values=values)

    def start_settings_monitoring(self):
        """开始监听设置变化"""
        # 保存当前设置
        self.last_settings = self.settings_frame.get_settings()
        
        # 定期检查设置是否变化
        self.check_settings_changes()
    
    def check_settings_changes(self):
        """检查设置是否变化"""
        # 如果窗口已关闭，退出监听
        if not self.winfo_exists():
            return
            
        try:
            # 获取当前设置
            current_settings = self.settings_frame.get_settings()
            
            # 检查语速是否改变
            if (self.last_settings.get("speed") != current_settings.get("speed") or
                self.last_settings.get("voice") != current_settings.get("voice")):
                # 更新预计时长
                self.update_estimated_times()
                # 更新保存的设置
                self.last_settings = current_settings
        except Exception as e:
            print(f"检查设置变化时出错: {str(e)}")
            
        # 设置下一次检查
        self.settings_change_timer = self.after(2000, self.check_settings_changes)
    
    def stop_settings_monitoring(self):
        """停止监听设置变化"""
        if self.settings_change_timer:
            self.after_cancel(self.settings_change_timer)
            self.settings_change_timer = None

    def destroy(self):
        """销毁对象时停止监听"""
        self.stop_settings_monitoring()
        super().destroy()

    def show_time_calculator(self):
        """显示简单的时长计算器对话框"""
        # 弹出输入对话框
        from tkinter import simpledialog
        
        # 获取用户输入的字数
        char_count = simpledialog.askinteger("时长计算器", "请输入文本字数:", 
                                             initialvalue=10000, 
                                             minvalue=1, 
                                             parent=self)
        
        if char_count:
            # 计算预计时长
            estimated_time = self.calculate_estimated_time(char_count)
            
            # 显示结果
            messagebox.showinfo("预计时长", f"{char_count} 字的预计处理时长为:\n\n{estimated_time}")

    # ------------------------------------------------------------------
    # 分割字数预设相关方法
    # ------------------------------------------------------------------
    def load_split_preset(self):
        """从磁盘加载分割字数预设"""
        try:
            if SPLIT_PRESET_FILE.exists():
                with open(SPLIT_PRESET_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                first_size = data.get("first_file_size")
                split_size = data.get("split_size")
                if isinstance(first_size, int) and first_size > 0:
                    self.first_file_size.set(str(first_size))
                if isinstance(split_size, int) and split_size > 0:
                    self.split_size.set(str(split_size))
        except Exception as e:
            print(f"加载分割字数预设失败: {e}")

    def save_split_preset(self):
        """保存当前分割字数为预设，如已存在则覆盖"""
        try:
            first_size = int(self.first_file_size.get())
            split_size = int(self.split_size.get())
            if first_size <= 0 or split_size <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("错误", "分割字数必须为大于0的整数")
            return

        data = {
            "first_file_size": first_size,
            "split_size": split_size
        }

        try:
            with open(SPLIT_PRESET_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("保存成功", "分割字数预设已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存预设失败: {e}")
            
            """
说明界面模块，用于显示软件使用说明
"""
# =================== 说明界面模块 ===================

class InstructionFrame(ttk.Frame):
    """说明界面，显示软件使用说明"""

    def __init__(self, parent):
        """
        初始化说明界面
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 创建界面组件
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = ttk.Frame(self)
        title_frame.pack(fill=tk.X, padx=10, pady=(20, 5))
        
        ttk.Label(title_frame, text="使用说明", 
                 font=("微软雅黑", 16, "bold")).pack(anchor=tk.CENTER)
        
        # 分隔线
        ttk.Separator(self, orient="horizontal").pack(fill=tk.X, padx=20, pady=10)
        
        # 说明文本区域
        text_frame = ttk.Frame(self)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 创建文本框和滚动条
        self.instruction_text = tk.Text(text_frame, 
                                       font=("微软雅黑", 10), 
                                       wrap=tk.WORD, 
                                       bg="#f9f9f9", 
                                       padx=10, 
                                       pady=10)
        scrollbar = ttk.Scrollbar(text_frame, command=self.instruction_text.yview)
        self.instruction_text.configure(yscrollcommand=scrollbar.set)
        
        # 放置组件
        self.instruction_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置文本为只读
        self.instruction_text.configure(state="normal")
        
        # 加载说明文本
        self.load_instruction()
        
        # 设置文本为只读
        self.instruction_text.configure(state="disabled")
        
    def load_instruction(self):
        """加载说明文本"""
        instruction_text = """
# 配音工具使用说明
#暂无说明
"""
        
        # 插入说明文本
        self.instruction_text.delete("1.0", tk.END)
        self.instruction_text.insert("1.0", instruction_text)
        
    def apply_text_styles(self):
        """应用文本样式，设置标题和重点文本的样式"""
        # 创建标签
        self.instruction_text.tag_configure("title1", font=("微软雅黑", 14, "bold"), foreground="#2196F3")
        self.instruction_text.tag_configure("title2", font=("微软雅黑", 12, "bold"), foreground="#0D47A1")
        self.instruction_text.tag_configure("title3", font=("微软雅黑", 11, "bold"), foreground="#1976D2")
        self.instruction_text.tag_configure("bold", font=("微软雅黑", 10, "bold"))
        
        # 应用样式
        lines = self.instruction_text.get("1.0", tk.END).split("\n")
        
        for i, line in enumerate(lines):
            line_num = i + 1
            
            # 处理大标题 (# 开头)
            if line.startswith("# "):
                self.instruction_text.tag_add("title1", f"{line_num}.0", f"{line_num}.end")
            
            # 处理二级标题 (## 开头)
            elif line.startswith("## "):
                self.instruction_text.tag_add("title2", f"{line_num}.0", f"{line_num}.end")
            
            # 处理三级标题 (### 开头)
            elif line.startswith("### "):
                self.instruction_text.tag_add("title3", f"{line_num}.0", f"{line_num}.end")
                
            # 处理加粗文本 (** 包围)
            else:
                # 查找所有 ** 包围的文本
                start = 0
                while True:
                    start = line.find("**", start)
                    if start == -1:
                        break
                    end = line.find("**", start + 2)
                    if end == -1:
                        break
                    
                    # 应用粗体样式
                    self.instruction_text.tag_add("bold", f"{line_num}.{start}", f"{line_num}.{end+2}")
                    
                    # 更新起始位置
                    start = end + 2


# =================== 设置界面模块 ===================
"""
设置界面模块，用于显示和管理配音设置
"""

class SettingsFrame(ttk.Frame):
    """配音设置界面，右侧面板"""

    def __init__(self, parent):
        """
        初始化设置面板
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 初始化设置数据（不加载实际设置，只设置默认值）
        self.init_settings_default()
        
        # 初始化Tkinter变量
        self.init_variables()
        
        # 现在尝试加载保存的设置
        self.load_settings() 
        
        # 创建界面组件
        self.create_widgets()
        
    def init_settings_default(self):
        """初始化默认设置数据（不包含加载）"""
        # 默认设置
        self.settings = {
            "voice": "zh-CN-XiaoxiaoNeural",
            "engine": "edge-tts",
            "speed": 1.0,  # 1.0 表示正常速度
            "pitch": 1.0,  # 1.0 表示正常音调
            "volume": 1.0,  # 1.0 表示正常音量
            "azure_key": "",
            "azure_region": "eastasia",
            "baidu_app_id": "",
            "baidu_api_key": "",
            "baidu_secret_key": "",
            "xunfei_app_id": "",
            "xunfei_api_key": "",
            "xunfei_api_secret": "",
            "tencent_secret_id": "",
            "tencent_secret_key": "",
            "amplify_db": 0,
            "normalize_audio": False,
            # 批量分割预设
            "first_split_size": 50000,
            "split_size": 50000,
        }
        
        # 从高级设置获取
        self.advanced_settings = self.load_advanced_settings()
        
        # 音色列表 - Edge TTS
        self.edge_voice_list = {
            "男声": [
                "zh-CN-YunxiNeural",      # 云希 - 男声
                "zh-CN-YunjianNeural",    # 云健 - 男声
            ],
            "女声": [
                "zh-CN-XiaoxiaoNeural",   # 晓晓 - 女声
                "zh-CN-XiaoyiNeural",     # 晓伊 - 女声
            ]
        }
        
        # 仅保留 Edge TTS 引擎
        self.engine_list = [
            "edge-tts",     # 微软 Edge TTS
        ]
    
    def init_variables(self):
        """初始化Tkinter变量"""
        # 创建基本变量
        self.voice_var = tk.StringVar(value=self.settings["voice"])
        self.engine_var = tk.StringVar(value=self.settings["engine"])
        self.speed_var = tk.DoubleVar(value=0)  # UI值，0表示正常速度
        self.pitch_var = tk.DoubleVar(value=0)  # UI值，0表示正常音调
        self.volume_var = tk.DoubleVar(value=50)  # UI值，50表示正常音量
        
        # API变量
        self.azure_key_var = tk.StringVar(value=self.settings.get("azure_key", ""))
        self.azure_region_var = tk.StringVar(value=self.settings.get("azure_region", ""))
        self.baidu_app_id_var = tk.StringVar(value=self.settings.get("baidu_app_id", ""))
        self.baidu_api_key_var = tk.StringVar(value=self.settings.get("baidu_api_key", ""))
        self.baidu_secret_key_var = tk.StringVar(value=self.settings.get("baidu_secret_key", ""))
        self.xunfei_app_id_var = tk.StringVar(value=self.settings.get("xunfei_app_id", ""))
        self.xunfei_api_key_var = tk.StringVar(value=self.settings.get("xunfei_api_key", ""))
        self.xunfei_api_secret_var = tk.StringVar(value=self.settings.get("xunfei_api_secret", ""))
        self.tencent_secret_id_var = tk.StringVar(value=self.settings.get("tencent_secret_id", ""))
        self.tencent_secret_key_var = tk.StringVar(value=self.settings.get("tencent_secret_key", ""))
        self.amplify_db_var = tk.StringVar(value=str(self.settings.get("amplify_db", "0")))
        self.normalize_audio_var = tk.BooleanVar(value=self.settings.get("normalize_audio", False))

        # 批量分割变量
        self.first_split_size_var = tk.StringVar(value=str(self.settings.get("first_split_size", 50000)))
        self.split_size_var = tk.StringVar(value=str(self.settings.get("split_size", 50000)))

    def create_widgets(self):
        """创建设置面板组件"""
        # 创建设置区域容器
        self.settings_container = ttk.Frame(self)
        self.settings_container.pack(fill=tk.BOTH, expand=True)
        
        # 添加标题
        self.create_header("配音设置", "#1565C0")
        
        # 创建设置组 - 语音选择
        voice_frame = self.create_section_frame("语音设置")
        
        # 添加TTS引擎选择
        ttk.Label(voice_frame, text="TTS引擎:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        engine_combo = ttk.Combobox(voice_frame, textvariable=self.engine_var, state="readonly", width=20)
        engine_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        engine_combo["values"] = self.engine_list
        engine_combo.bind("<<ComboboxSelected>>", self.on_engine_change)
        
        # 添加语音选择
        ttk.Label(voice_frame, text="语音:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.voice_combo = ttk.Combobox(voice_frame, textvariable=self.voice_var, state="readonly", width=20)
        self.voice_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 初始化语音选项
        self.update_voice_list()
        
        # 创建API设置组 (已废弃，仅为兼容保留，不显示在界面中)
        self.api_frame = self.create_section_frame("API设置")
        self.api_widgets = {}  # 存储不同引擎的API设置组件
        
        # 创建Edge TTS API设置组件 (无需API密钥)
        edge_frame = ttk.Frame(self.api_frame)
        ttk.Label(edge_frame, text="Edge TTS无需API密钥，可直接使用", foreground="#1976D2").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["edge-tts"] = edge_frame
        
        # 隐藏API设置分组
        self.api_frame.pack_forget()
        
        # API 设置已废弃，避免后续逻辑重复显示
        self.update_api_settings()
        
        # 创建设置组 - 语音参数
        params_frame = self.create_section_frame("语音参数")
        
        # 音量滑块 (UI范围: 0-100, 实际值范围: 0.1-2.0)
        volume_frame = ttk.Frame(params_frame)
        volume_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(volume_frame, text="音量:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        volume_scale = ttk.Scale(volume_frame, from_=0, to=100, orient=tk.HORIZONTAL, 
                               variable=self.volume_var, length=120,  # 增加长度到120
                               command=self.on_volume_change)
        volume_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        volume_value_frame = ttk.Frame(volume_frame)
        volume_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.volume_entry = ttk.Entry(volume_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.volume_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.volume_entry.insert(0, str(round(self.volume_var.get())))
        self.volume_entry.bind('<Return>', self.on_volume_entry_change)
        self.volume_entry.bind('<FocusOut>', self.on_volume_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(volume_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)
        
        # 语速滑块 (UI范围: -100-100, 实际值范围: 0.5-2.0)
        speed_frame = ttk.Frame(params_frame)
        speed_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(speed_frame, text="语速:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        speed_scale = ttk.Scale(speed_frame, from_=-100, to=100, orient=tk.HORIZONTAL, 
                              variable=self.speed_var, length=120, # 增加长度到120
                              command=self.on_speed_change)
        speed_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        speed_value_frame = ttk.Frame(speed_frame)
        speed_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.speed_entry = ttk.Entry(speed_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.speed_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.speed_entry.insert(0, str(round(self.speed_var.get())))
        self.speed_entry.bind('<Return>', self.on_speed_entry_change)
        self.speed_entry.bind('<FocusOut>', self.on_speed_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(speed_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)

        # 音调滑块 (UI范围: -100-100, 实际值范围: 0.5-2.0)
        pitch_frame = ttk.Frame(params_frame)
        pitch_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(pitch_frame, text="音调:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        pitch_scale = ttk.Scale(pitch_frame, from_=-100, to=100, orient=tk.HORIZONTAL, 
                              variable=self.pitch_var, length=120, # 增加长度到120
                              command=self.on_pitch_change)
        pitch_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        pitch_value_frame = ttk.Frame(pitch_frame)
        pitch_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.pitch_entry = ttk.Entry(pitch_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.pitch_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.pitch_entry.insert(0, str(round(self.pitch_var.get())))
        self.pitch_entry.bind('<Return>', self.on_pitch_entry_change)
        self.pitch_entry.bind('<FocusOut>', self.on_pitch_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(pitch_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)

        # 音量放大（dB）输入框 - 移动到音调下方
        amplify_frame = ttk.Frame(params_frame)
        amplify_frame.pack(fill=tk.X, pady=5)
        ttk.Label(amplify_frame, text="音量放大:", width=8).pack(side=tk.LEFT, padx=5)
        self.amplify_entry = ttk.Entry(amplify_frame, textvariable=self.amplify_db_var, width=6, justify='center', style="ValueDisplay.TEntry")
        self.amplify_entry.pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(amplify_frame, text="dB", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)
        # 绑定回车和失焦事件，校验输入
        self.amplify_entry.bind('<Return>', self.on_amplify_entry_change)
        self.amplify_entry.bind('<FocusOut>', self.on_amplify_entry_change)
        
        # 音量标准化复选框 - 移动到音调下方
        normalize_check = ttk.Checkbutton(params_frame, text="启用音量标准化", variable=self.normalize_audio_var)
        normalize_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(self.settings_container)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 保存设置按钮
        save_btn = ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=10)
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 加载设置按钮改为重置设置按钮
        reset_btn = ttk.Button(button_frame, text="重置设置", command=self.reset_settings, width=10)
        reset_btn.pack(side=tk.RIGHT, padx=5)
        
        # 创建完所有组件后，更新UI显示以反映已加载的设置
        self.update_ui_after_load()
    
    def create_header(self, text, color="#333333"):
        """创建带有颜色的标题"""
        header_frame = ttk.Frame(self.settings_container)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        header_label = ttk.Label(header_frame, 
                               text=text, 
                               font=("微软雅黑", 18, "bold"),
                               foreground=color)
        header_label.pack(anchor=tk.W)
        
        # 添加分隔线
        separator = ttk.Separator(self.settings_container, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 10))
        
    def create_section_frame(self, title):
        """创建带标题的设置分组"""
        # 创建分组容器
        section_frame = ttk.LabelFrame(self.settings_container, text=title)
        section_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        return section_frame
    
    def on_engine_change(self, event=None):
        """引擎变化时的回调"""
        # 更新API设置
        self.update_api_settings()
        
        # 更新语音列表
        self.update_voice_list()
    
    def update_api_settings(self):
        """根据当前选择的引擎显示对应的API设置"""
        # API 设置面板已在新版中隐藏，此处直接返回
        return
    
    def update_voice_list(self):
        """更新语音下拉框的选项"""
        # 获取当前选择的引擎
        engine = self.engine_var.get()
        
        # 若读取到的引擎不在支持列表中（旧版本遗留设置），强制切换回 edge-tts
        if engine not in self.engine_list:
            engine = "edge-tts"
            self.engine_var.set(engine)
        
        # 根据引擎获取对应的语音列表
        if engine == "edge-tts":
            # Edge TTS使用分组语音列表
            voices = []
            for category, voice_list in self.edge_voice_list.items():
                for voice in voice_list:
                    voices.append(voice)
        else:
            voices = []
        
        # 更新语音下拉框的值
        self.voice_combo["values"] = voices
        
        # 如果当前的语音不在列表中，选择第一个
        if self.voice_var.get() not in voices and voices:
            self.voice_var.set(voices[0])
    
    def on_speed_change(self, value):
        """语速变化时的回调"""
        # 获取UI值 (-100到100)
        speed_ui = round(float(value))
        
        # 更新UI标签
        self.speed_entry.delete(0, tk.END)
        self.speed_entry.insert(0, str(speed_ui))
        
        # 将UI值转换为实际值 (0.5-2.0)
        # -100 对应 0.5, 0 对应 1.0, 100 对应 2.0
        actual_speed = 1.0 + (speed_ui / 100.0)
        # 限制范围
        actual_speed = max(0.5, min(2.0, actual_speed))
        
        # 更新设置
        self.settings["speed"] = actual_speed
    
    def on_pitch_change(self, value):
        """音调变化时的回调"""
        # 获取UI值 (-100到100)
        pitch_ui = round(float(value))
        
        # 更新UI标签
        self.pitch_entry.delete(0, tk.END)
        self.pitch_entry.insert(0, str(pitch_ui))
        
        # 将UI值转换为实际值 (0.5-2.0)
        # -100 对应 0.5, 0 对应 1.0, 100 对应 2.0
        actual_pitch = 1.0 + (pitch_ui / 100.0)
        # 限制范围
        actual_pitch = max(0.5, min(2.0, actual_pitch))
        
        # 更新设置
        self.settings["pitch"] = actual_pitch
    
    def on_volume_change(self, value):
        """音量变化时的回调"""
        # 获取UI值 (0到100)
        volume_ui = round(float(value))
        
        # 更新UI标签
        self.volume_entry.delete(0, tk.END)
        self.volume_entry.insert(0, str(volume_ui))
        
        # 将UI值转换为实际值 (0.1-2.0)
        # 0 对应 0.1, 100 对应 2.0
        actual_volume = 0.1 + (volume_ui / 100.0) * 1.9
        
        # 更新设置
        self.settings["volume"] = actual_volume
        
        # 记录变更
        self.settings["voice"] = self.voice_var.get()
        self.settings["engine"] = self.engine_var.get()
        
        return self.settings

    
    def save_settings(self):
        """保存设置到文件"""
        try:
            # 更新所有设置值
            self.settings.update({
                "voice": self.voice_var.get(),
                "speed": self.settings["speed"],
                "pitch": self.settings["pitch"],
                "volume": self.settings["volume"],
                "engine": self.engine_var.get(),
                "amplify_db": float(self.amplify_db_var.get()),  # 新增音量放大dB
                "normalize_audio": self.normalize_audio_var.get(), # 新增音量标准化
                # 批量分割
                "first_split_size": int(self.first_split_size_var.get()),
                "split_size": int(self.split_size_var.get()),
            })
            

            
            # 获取设置文件路径
            settings_dir = os.path.expanduser("~/.tts_tool")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            settings_file = os.path.join(settings_dir, "settings.json")
            
            # 保存为JSON
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(self.settings, f, indent=4)
            
            # 显示成功消息
            messagebox.showinfo("保存成功", "设置已保存")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("保存失败", f"保存设置失败: {str(e)}")
    
    def load_settings(self):
        """从文件加载设置"""
        try:
            # 获取设置文件路径
            settings_file = os.path.join(os.path.expanduser("~/.tts_tool"), "settings.json")
            
            # 如果文件存在，加载设置
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)
                
                # 更新配置
                for key, value in loaded_settings.items():
                    if key in self.settings:
                        self.settings[key] = value
                
                # 新增同步amplify_db
                if "amplify_db" in loaded_settings:
                    self.settings["amplify_db"] = loaded_settings["amplify_db"]
                
                # 新增同步normalize_audio
                if "normalize_audio" in loaded_settings:
                    self.settings["normalize_audio"] = loaded_settings["normalize_audio"]
                
                # 新增同步first_split_size
                if "first_split_size" in loaded_settings:
                    self.settings["first_split_size"] = loaded_settings["first_split_size"]
                
                # 新增同步split_size
                if "split_size" in loaded_settings:
                    self.settings["split_size"] = loaded_settings["split_size"]
                
                # 确保所有需要的变量都已经初始化
                if not hasattr(self, 'voice_var') or not hasattr(self, 'engine_var'):
                    print("变量尚未初始化，跳过UI更新")
                    return
                
                # 更新变量
                try:
                    self.voice_var.set(self.settings["voice"])
                    self.engine_var.set(self.settings["engine"])
                    
                    # 计算UI显示值
                    # 音量: 0.1-2.0 -> 0-100
                    volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
                    self.volume_var.set(volume_ui)
                    
                    # 语速: 0.5-2.0 -> -100-100
                    speed_ui = (self.settings["speed"] - 1.0) * 100
                    self.speed_var.set(speed_ui)
                    
                    # 音调: 0.5-2.0 -> -100-100
                    pitch_ui = (self.settings["pitch"] - 1.0) * 100
                    self.pitch_var.set(pitch_ui)
                    
                    # 新增同步amplify_db到输入框
                    self.amplify_db_var.set(str(self.settings.get("amplify_db", "0")))
                    
                    # 新增同步normalize_audio到复选框
                    self.normalize_audio_var.set(self.settings.get("normalize_audio", False))
                    
                    # 新增同步first_split_size到输入框
                    self.first_split_size_var.set(str(self.settings.get("first_split_size", 50000)))
                    
                    # 新增同步split_size到输入框
                    self.split_size_var.set(str(self.settings.get("split_size", 50000)))
                    
                    # 更新API设置
                    self.azure_key_var.set(self.settings.get("azure_key", ""))
                    self.azure_region_var.set(self.settings.get("azure_region", ""))
                    self.baidu_app_id_var.set(self.settings.get("baidu_app_id", ""))
                    self.baidu_api_key_var.set(self.settings.get("baidu_api_key", ""))
                    self.baidu_secret_key_var.set(self.settings.get("baidu_secret_key", ""))
                    self.xunfei_app_id_var.set(self.settings.get("xunfei_app_id", ""))
                    self.xunfei_api_key_var.set(self.settings.get("xunfei_api_key", ""))
                    self.xunfei_api_secret_var.set(self.settings.get("xunfei_api_secret", ""))
                    self.tencent_secret_id_var.set(self.settings.get("tencent_secret_id", ""))
                    self.tencent_secret_key_var.set(self.settings.get("tencent_secret_key", ""))
                    
                    print("成功加载设置")
                except Exception as e:
                    # 捕获任何可能的UI更新错误
                    print(f"更新UI变量失败: {str(e)}")
                    # 继续流程，不抛出异常
        except Exception as e:
            print(f"加载设置失败: {str(e)}")
            # 继续流程，不抛出异常
    
    def load_advanced_settings(self):
        """加载高级设置"""
        try:
            # 获取高级设置文件路径 - 使用新的文件名
            settings_dir = os.path.expanduser("~/.tts_tool")
            settings_file = os.path.join(settings_dir, "tts_advanced_config.json")

            # 检查是否存在旧的配置文件
            old_settings_file = os.path.join(settings_dir, "advanced_settings.json")

            # 设置默认值
            advanced_settings = {
                "remove_empty_lines": True,
                "concurrent_tasks": 8,  # 提高默认值为8，更好利用CPU
                "retry_count": 5,
                "retry_interval": 10,
                "batch_size": 3000,  # 修改默认批次大小为3000
            }
            
            # 如果新文件存在，加载设置
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)

                # 更新配置
                for key, value in loaded_settings.items():
                    if key in advanced_settings:
                        advanced_settings[key] = value
            elif os.path.exists(old_settings_file):
                # 如果新文件不存在但旧文件存在，加载旧文件
                with open(old_settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)

                # 更新配置
                for key, value in loaded_settings.items():
                    if key in advanced_settings:
                        advanced_settings[key] = value
                print("检测到旧的高级设置文件，建议重新保存设置以迁移到新文件名")
                        
            return advanced_settings
        except Exception as e:
            print(f"加载高级设置失败: {str(e)}")
            # 返回默认值
            return {
                "remove_empty_lines": True,
                "concurrent_tasks": 8,  # 提高默认值为8，更好利用CPU
                "retry_count": 5,
                "retry_interval": 10,
                "batch_size": 10000,
            }
    
    def get_settings(self):
        """获取当前设置"""
        # 更新设置
        self.settings.update({
            "voice": self.voice_var.get(),
            "speed": self.settings["speed"],
            "pitch": self.settings["pitch"],
            "volume": self.settings["volume"],
            "engine": self.engine_var.get(),
            "amplify_db": float(self.amplify_db_var.get()),  # 新增音量放大dB
            "normalize_audio": self.normalize_audio_var.get(), # 新增音量标准化
            # API设置
            "azure_key": self.azure_key_var.get(),
            "azure_region": self.azure_region_var.get(),
            "baidu_app_id": self.baidu_app_id_var.get(),
            "baidu_api_key": self.baidu_api_key_var.get(),
            "baidu_secret_key": self.baidu_secret_key_var.get(),
            "xunfei_app_id": self.xunfei_app_id_var.get(),
            "xunfei_api_key": self.xunfei_api_key_var.get(),
            "xunfei_api_secret": self.xunfei_api_secret_var.get(),
            "tencent_secret_id": self.tencent_secret_id_var.get(),
            "tencent_secret_key": self.tencent_secret_key_var.get(),
            # 批量分割
            "first_split_size": int(self.first_split_size_var.get()),
            "split_size": int(self.split_size_var.get()),
        })
        
        # 合并高级设置
        all_settings = {**self.settings, **self.advanced_settings}
        
        return all_settings

    # 新增Entry回调函数
    def on_volume_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.volume_entry.get())
            # 限制在0-100范围内
            value = max(0, min(100, value))
            # 更新滑块和变量
            self.volume_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_volume_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.volume_entry.delete(0, tk.END)
            self.volume_entry.insert(0, str(round(self.volume_var.get())))
    
    def on_speed_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.speed_entry.get())
            # 限制在-100到100范围内
            value = max(-100, min(100, value))
            # 更新滑块和变量
            self.speed_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_speed_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.speed_entry.delete(0, tk.END)
            self.speed_entry.insert(0, str(round(self.speed_var.get())))
    
    def on_pitch_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.pitch_entry.get())
            # 限制在-100到100范围内
            value = max(-100, min(100, value))
            # 更新滑块和变量
            self.pitch_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_pitch_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.pitch_entry.delete(0, tk.END)
            self.pitch_entry.insert(0, str(round(self.pitch_var.get())))
    
    def on_amplify_entry_change(self, event=None):
        """音量放大dB输入框回调，校验并同步到变量"""
        try:
            value = float(self.amplify_entry.get())
            # 限制范围（-30到+30dB，防止极端输入）
            value = max(-30, min(30, value))
            self.amplify_db_var.set(str(value))
            self.settings["amplify_db"] = value
        except ValueError:
            # 恢复原值
            self.amplify_entry.delete(0, tk.END)
            self.amplify_entry.insert(0, str(self.settings.get("amplify_db", "0")))
    
    def reset_settings(self):
        """重置所有设置为默认值"""
        try:
            # 确认是否重置
            if messagebox.askyesno("确认重置", "确定要重置所有设置为默认值吗？"):
                # 重置为默认值
                self.settings = {
                    "voice": "zh-CN-XiaoxiaoNeural",
                    "engine": "edge-tts",
                    "speed": 1.0,
                    "pitch": 1.0,
                    "volume": 1.0,
                    "azure_key": "",
                    "azure_region": "eastasia",
                    "baidu_app_id": "",
                    "baidu_api_key": "",
                    "baidu_secret_key": "",
                    "xunfei_app_id": "",
                    "xunfei_api_key": "",
                    "xunfei_api_secret": "",
                    "tencent_secret_id": "",
                    "tencent_secret_key": "",
                    "amplify_db": 0,
                    "normalize_audio": False,
                    # 批量分割预设
                    "first_split_size": 50000,
                    "split_size": 50000,
                }
                
                # 更新UI变量
                # 更新基本设置变量
                self.voice_var.set(self.settings["voice"])
                self.engine_var.set(self.settings["engine"])
                
                # 计算并更新UI滑块位置
                # 音量: 0.1-2.0 -> 0-100
                volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
                # 语速: 0.5-2.0 -> -100-100
                speed_ui = (self.settings["speed"] - 1.0) * 100
                # 音调: 0.5-2.0 -> -100-100
                pitch_ui = (self.settings["pitch"] - 1.0) * 100
                
                # 设置UI变量
                self.volume_var.set(volume_ui)
                self.speed_var.set(speed_ui)
                self.pitch_var.set(pitch_ui)
                
                # 新增同步amplify_db到输入框
                self.amplify_db_var.set(str(self.settings.get("amplify_db", "0")))
                
                # 新增同步normalize_audio到复选框
                self.normalize_audio_var.set(self.settings.get("normalize_audio", False))
                
                # 新增同步first_split_size到输入框
                self.first_split_size_var.set(str(self.settings.get("first_split_size", 50000)))
                
                # 新增同步split_size到输入框
                self.split_size_var.set(str(self.settings.get("split_size", 50000)))
                
                # 更新标签显示
                self.speed_entry.delete(0, tk.END)
                self.speed_entry.insert(0, str(round(speed_ui)))
                
                self.pitch_entry.delete(0, tk.END)
                self.pitch_entry.insert(0, str(round(pitch_ui)))
                
                self.volume_entry.delete(0, tk.END)
                self.volume_entry.insert(0, str(round(volume_ui)))
                
                # 重置API设置变量
                self.azure_key_var.set("")
                self.azure_region_var.set("")
                self.baidu_app_id_var.set("")
                self.baidu_api_key_var.set("")
                self.baidu_secret_key_var.set("")
                self.xunfei_app_id_var.set("")
                self.xunfei_api_key_var.set("")
                self.xunfei_api_secret_var.set("")
                self.tencent_secret_id_var.set("")
                self.tencent_secret_key_var.set("")
                
                # 更新API设置显示
                self.update_api_settings()
                
                # 更新语音列表
                self.update_voice_list()
                
                # 显示成功消息
                messagebox.showinfo("重置成功", "所有设置已重置为默认值")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("重置失败", f"重置设置失败: {str(e)}")
    
    def update_ui_after_load(self):
        """在创建完UI组件后更新显示"""
        try:
            # 根据引擎更新UI
            self.update_voice_list()
            self.update_api_settings()
            
            # 更新数值显示
            volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
            self.volume_entry.delete(0, tk.END)
            self.volume_entry.insert(0, str(round(volume_ui)))
            
            speed_ui = (self.settings["speed"] - 1.0) * 100
            self.speed_entry.delete(0, tk.END)
            self.speed_entry.insert(0, str(round(speed_ui)))
            
            pitch_ui = (self.settings["pitch"] - 1.0) * 100
            self.pitch_entry.delete(0, tk.END)
            self.pitch_entry.insert(0, str(round(pitch_ui)))
        except Exception as e:
            print(f"更新UI显示失败: {str(e)}")
            # 继续流程，不抛出异常 
            
            """
单独配音界面模块，用于输入文本并生成配音
"""
# =================== 单独配音界面模块 ===================



class SingleTTSFrame(ttk.Frame):
    """单独配音界面，支持文本输入和配音生成"""

    def __init__(self, parent, settings_frame):
        """
        初始化单独配音界面
        
        参数:
            parent: 父窗口
            settings_frame: 设置界面实例
        """
        super().__init__(parent)
        
        # 保存设置界面引用
        self.settings_frame = settings_frame
        
        # 初始化变量
        self.init_variables()
        
        # 创建界面组件
        self.create_widgets()
        
        # 初始化音频播放
        self.init_audio()
        
    def init_variables(self):
        """初始化变量"""
        # 文本内容
        self.text_content = ""
        
        # 处理状态
        self.is_processing = False
        self.is_playing = False
        self.preview_file = None
        self.cancel_requested = False  # 新增：取消标志
        
        # 进度信息
        self.progress_value = 0
        self.status_text = "准备就绪"
        
    def init_audio(self):
        """初始化音频播放"""
        # 初始化pygame混音器
        try:
            pygame.mixer.init()
        except Exception as e:
            print(f"初始化音频播放失败: {str(e)}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 文本区域
        self.create_text_area(main_container)
        
        # 控制区域
        self.create_control_area(main_container)
        
        # 状态区域
        self.create_status_area(main_container)
        
    def create_text_area(self, parent):
        """创建文本输入区域"""
        # 文本区域标题
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(title_frame, text="文本内容", 
                 font=("微软雅黑", 12, "bold")).pack(side=tk.LEFT)
        
        # 字数统计
        self.char_count_var = tk.StringVar(value="字数: 0")
        ttk.Label(title_frame, textvariable=self.char_count_var).pack(side=tk.RIGHT)
        
        # 文本输入框
        text_frame = ttk.Frame(parent)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框和滚动条
        self.text_input = tk.Text(text_frame, font=("微软雅黑", 10), wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, command=self.text_input.yview)
        self.text_input.configure(yscrollcommand=scrollbar.set)
        
        # 放置组件
        self.text_input.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.text_input.bind("<KeyRelease>", self.update_char_count)
        
    def create_control_area(self, parent):
        """创建控制区域"""
        control_area = ttk.Frame(parent)
        control_area.pack(fill=tk.X, pady=10)
        
        button_frame = ttk.Frame(control_area)
        button_frame.pack(fill=tk.X)
        
        # 加载文件按钮
        load_button = ttk.Button(
            button_frame,
            text="加载文本",
            command=self.load_text_file,
            width=15
        )
        load_button.pack(side=tk.LEFT, padx=5)
        
        # 清空按钮
        clear_button = ttk.Button(
            button_frame,
            text="清空文本",
            command=self.clear_text,
            width=15
        )
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # 开始生成按钮
        self.generate_button = ttk.Button(
            button_frame,
            text="开始生成",
            command=self.generate_tts,
            width=15
        )
        self.generate_button.pack(side=tk.LEFT, padx=5)
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self.cancel_process,
            width=15,
            state=tk.DISABLED
        )
        self.cancel_button.pack(side=tk.LEFT, padx=5)
        
        return control_area

    def create_status_area(self, parent):
        """创建状态显示区域"""
        # 状态区域
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, 
                                          variable=self.progress_var, 
                                          maximum=100, 
                                          length=200, 
                                          mode='determinate')
        self.progress_bar.pack(side=tk.TOP, fill=tk.X, pady=5)
        
        # 状态栏
        status_bar = ttk.Frame(status_frame)
        status_bar.pack(fill=tk.X)
        
        # 状态文本
        self.status_var = tk.StringVar(value=self.status_text)
        ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT)
        
    def update_char_count(self, event=None):
        """更新字数统计"""
        text = self.text_input.get("1.0", tk.END)
        count = len(text.strip())
        self.char_count_var.set(f"字数: {count}")
        
    def load_text_file(self):
        """加载文本文件"""
        file_path = filedialog.askopenfilename(
            title="选择文本文件",
            filetypes=(("文本文件", "*.txt"), ("所有文件", "*.*"))
        )
        
        if file_path:
            try:
                text_processor = TextProcessor()
                text = text_processor.load_text_file(file_path)
                
                # 清空当前文本并插入新内容
                self.text_input.delete("1.0", tk.END)
                self.text_input.insert("1.0", text)
                
                # 更新字数统计
                self.update_char_count()
                
                # 更新状态
                self.status_var.set(f"已加载文件: {os.path.basename(file_path)}")
                
            except Exception as e:
                messagebox.showerror("加载失败", f"无法加载文件: {str(e)}")
                
    def clear_text(self):
        """清空文本内容"""
        self.text_input.delete("1.0", tk.END)
        self.update_char_count()
        self.status_var.set("已清空文本内容")
        
    def generate_tts(self):
        """生成TTS音频文件"""
        # 获取文本内容
        text = self.text_input.get("1.0", tk.END).strip()
        if not text:
            messagebox.showwarning("警告", "请输入文本内容！")
            return
        
        # 获取设置
        settings = self.settings_frame.get_settings()
        
        # 选择输出文件路径
        initial_dir = os.path.dirname(os.path.abspath(__file__))
        output_path = filedialog.asksaveasfilename(
            title="保存语音文件",
            filetypes=[("MP3 文件", "*.mp3"), ("WAV 文件", "*.wav"), ("所有文件", "*.*")],
            defaultextension=".mp3",
            initialdir=initial_dir
        )
        
        if not output_path:
            return
        
        # 更新界面状态
        self.generate_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.is_processing = True
        self.cancel_requested = False
        
        # 使用status_var和progress_var代替update_progress
        self.status_var.set("开始处理...")
        self.progress_var.set(5)
        
        # 创建线程处理生成
        thread = threading.Thread(
            target=self._generate_in_thread,
            args=(text, output_path, settings)
        )
        thread.daemon = True
        thread.start()
        
    def _generate_in_thread(self, text, output_path, settings):
        """在线程中处理配音生成"""
        try:
            # 处理文本
            text_processor = TextProcessor()
            processed_text = text_processor.process_text(text, settings["remove_empty_lines"])
            
            # 检查是否请求取消
            if self.cancel_requested:
                self.status_var.set("操作已取消")
                self.progress_var.set(0)
                return
            
            # 创建TTS引擎实例
            tts_engine = TTSEngine(settings)
            batch_processor = BatchProcessor(settings)
            
            # 更新进度回调
            def update_progress(value, status):
                self.progress_var.set(value)
                self.status_var.set(status)
                
                # 检查是否请求取消，如果是则停止TTS引擎和批处理器
                if self.cancel_requested:
                    tts_engine.stop()
                    batch_processor.stop()
                    return True  # 返回True表示请求取消
                
                return False  # 返回False表示继续处理
            
            # 使用批处理器进行分批处理
            self.status_var.set("正在分析文本...")
            
            # 检查是否请求取消
            if self.cancel_requested:
                self.status_var.set("操作已取消")
                self.progress_var.set(0)
                return
            
            # 检查文本长度是否需要分批处理
            if len(processed_text) > settings.get("batch_size", 10000):
                self.status_var.set("文本较长，将使用分批处理...")
                batch_processor.process_text_in_batches(
                    processed_text, 
                    output_path, 
                    update_progress
                )
            else:
                # 文本较短，使用单一TTS引擎处理
                self.status_var.set("开始生成配音...")
                # 生成音频
                tts_engine.generate_audio(processed_text, output_path, update_progress)
            
            # 检查是否请求取消
            if self.cancel_requested:
                self.status_var.set("操作已取消")
                self.progress_var.set(0)
                # 如果生成了部分文件，则删除
                if os.path.exists(output_path):
                    try:
                        os.remove(output_path)
                    except:
                        pass
                # 删除相关的SRT文件
                srt_file = os.path.splitext(output_path)[0] + ".srt"
                if os.path.exists(srt_file):
                    try:
                        os.remove(srt_file)
                    except:
                        pass
                return
            
            # 更新状态
            self.status_var.set("配音生成完成")
            self.progress_var.set(100)
            
            # 显示成功消息
            messagebox.showinfo("生成成功", f"配音文件已保存至: {output_path}")
            
        except Exception as e:
            # 更新状态
            self.status_var.set(f"生成失败: {str(e)}")
            messagebox.showerror("生成失败", f"生成配音失败: {str(e)}")
            
        finally:
            # 恢复按钮状态
            self.generate_button.configure(text="开始生成")
            self.generate_button.configure(state="normal")
            self.cancel_button.configure(state=tk.DISABLED)  # 禁用取消按钮
            self.is_processing = False
            self.cancel_requested = False  # 重置取消标志
            
    def cancel_process(self):
        """取消配音生成处理"""
        if not self.is_processing:
            return
            
        # 设置取消请求标志
        self.cancel_requested = True
        
        # 更新状态
        self.status_var.set("正在取消处理...")
        
        # 禁用取消按钮，避免重复点击
        self.cancel_button.configure(state=tk.DISABLED)