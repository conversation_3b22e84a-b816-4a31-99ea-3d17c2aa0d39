"""
设置界面模块，用于显示和管理配音设置
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import re
from ..core.tts_engine import TTSEngine

class SettingsFrame(ttk.Frame):
    """配音设置界面，右侧面板"""

    def __init__(self, parent):
        """
        初始化设置面板
        
        参数:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 初始化设置数据（不加载实际设置，只设置默认值）
        self.init_settings_default()
        
        # 初始化Tkinter变量
        self.init_variables()
        
        # 现在尝试加载保存的设置
        self.load_settings() 
        
        # 创建界面组件
        self.create_widgets()
        
    def init_settings_default(self):
        """初始化默认设置数据（不包含加载）"""
        # 默认设置
        self.settings = {
            "voice": "zh-CN-XiaoxiaoNeural",
            "engine": "edge-tts",
            "speed": 1.0,  # 1.0 表示正常速度
            "pitch": 1.0,  # 1.0 表示正常音调
            "volume": 1.0,  # 1.0 表示正常音量
            "azure_key": "",
            "azure_region": "eastasia",
            "baidu_app_id": "",
            "baidu_api_key": "",
            "baidu_secret_key": "",
            "xunfei_app_id": "",
            "xunfei_api_key": "",
            "xunfei_api_secret": "",
            "tencent_secret_id": "",
            "tencent_secret_key": "",
            "amplify_db": 0,
            "normalize_audio": False,
            # 批量分割预设
            "first_split_size": 50000,
            "split_size": 50000,
        }
        
        # 从高级设置获取
        self.advanced_settings = self.load_advanced_settings()
        
        # 音色列表 - Edge TTS
        self.edge_voice_list = {
            "男声": [
                "zh-CN-YunxiNeural",      # 云希 - 男声
                "zh-CN-YunjianNeural",    # 云健 - 男声
            ],
            "女声": [
                "zh-CN-XiaoxiaoNeural",   # 晓晓 - 女声
                "zh-CN-XiaoyiNeural",     # 晓伊 - 女声
            ]
        }
        
        # 其他引擎的音色列表
        self.azure_voice_list = ["zh-CN-XiaoxiaoNeural", "zh-CN-YunxiNeural", "zh-CN-YunjianNeural", "zh-CN-XiaoyiNeural"]
        self.baidu_voice_list = ["度小宇", "度小美", "度逍遥", "度丫丫"]
        self.xunfei_voice_list = ["讯飞小燕", "讯飞许久", "讯飞小萍", "讯飞小婧"]
        self.tencent_voice_list = ["腾讯云小微", "腾讯云小刚", "腾讯云小红", "腾讯云小梦"]
        
        # 仅保留 Edge TTS 引擎
        self.engine_list = [
            "edge-tts",     # 微软 Edge TTS
        ]
    
    def init_variables(self):
        """初始化Tkinter变量"""
        # 创建基本变量
        self.voice_var = tk.StringVar(value=self.settings["voice"])
        self.engine_var = tk.StringVar(value=self.settings["engine"])
        self.speed_var = tk.DoubleVar(value=0)  # UI值，0表示正常速度
        self.pitch_var = tk.DoubleVar(value=0)  # UI值，0表示正常音调
        self.volume_var = tk.DoubleVar(value=50)  # UI值，50表示正常音量
        
        # API变量
        self.azure_key_var = tk.StringVar(value=self.settings.get("azure_key", ""))
        self.azure_region_var = tk.StringVar(value=self.settings.get("azure_region", ""))
        self.baidu_app_id_var = tk.StringVar(value=self.settings.get("baidu_app_id", ""))
        self.baidu_api_key_var = tk.StringVar(value=self.settings.get("baidu_api_key", ""))
        self.baidu_secret_key_var = tk.StringVar(value=self.settings.get("baidu_secret_key", ""))
        self.xunfei_app_id_var = tk.StringVar(value=self.settings.get("xunfei_app_id", ""))
        self.xunfei_api_key_var = tk.StringVar(value=self.settings.get("xunfei_api_key", ""))
        self.xunfei_api_secret_var = tk.StringVar(value=self.settings.get("xunfei_api_secret", ""))
        self.tencent_secret_id_var = tk.StringVar(value=self.settings.get("tencent_secret_id", ""))
        self.tencent_secret_key_var = tk.StringVar(value=self.settings.get("tencent_secret_key", ""))
        self.amplify_db_var = tk.StringVar(value=str(self.settings.get("amplify_db", "0")))
        self.normalize_audio_var = tk.BooleanVar(value=self.settings.get("normalize_audio", False))

        # 批量分割变量
        self.first_split_size_var = tk.StringVar(value=str(self.settings.get("first_split_size", 50000)))
        self.split_size_var = tk.StringVar(value=str(self.settings.get("split_size", 50000)))

    def create_widgets(self):
        """创建设置面板组件"""
        # 创建设置区域容器
        self.settings_container = ttk.Frame(self)
        self.settings_container.pack(fill=tk.BOTH, expand=True)
        
        # 添加标题
        self.create_header("配音设置", "#1565C0")
        
        # 创建设置组 - 语音选择
        voice_frame = self.create_section_frame("语音设置")
        
        # 添加TTS引擎选择
        ttk.Label(voice_frame, text="TTS引擎:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        engine_combo = ttk.Combobox(voice_frame, textvariable=self.engine_var, state="readonly", width=20)
        engine_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        engine_combo["values"] = self.engine_list
        engine_combo.bind("<<ComboboxSelected>>", self.on_engine_change)
        
        # 添加语音选择
        ttk.Label(voice_frame, text="语音:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.voice_combo = ttk.Combobox(voice_frame, textvariable=self.voice_var, state="readonly", width=20)
        self.voice_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 初始化语音选项
        self.update_voice_list()
        
        # 创建API设置组 (已废弃，仅为兼容保留，不显示在界面中)
        self.api_frame = self.create_section_frame("API设置")
        self.api_widgets = {}  # 存储不同引擎的API设置组件
        
        # 创建Azure TTS API设置组件
        azure_frame = ttk.Frame(self.api_frame)
        ttk.Label(azure_frame, text="API密钥:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(azure_frame, textvariable=self.azure_key_var, width=25).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(azure_frame, text="区域:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(azure_frame, textvariable=self.azure_region_var, width=25).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["azure-tts"] = azure_frame
        
        # 创建百度TTS API设置组件
        baidu_frame = ttk.Frame(self.api_frame)
        ttk.Label(baidu_frame, text="APP ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(baidu_frame, textvariable=self.baidu_app_id_var, width=25).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(baidu_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(baidu_frame, textvariable=self.baidu_api_key_var, width=25).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(baidu_frame, text="Secret Key:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(baidu_frame, textvariable=self.baidu_secret_key_var, width=25).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["baidu-tts"] = baidu_frame
        
        # 创建讯飞TTS API设置组件
        xunfei_frame = ttk.Frame(self.api_frame)
        ttk.Label(xunfei_frame, text="APP ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(xunfei_frame, textvariable=self.xunfei_app_id_var, width=25).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(xunfei_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(xunfei_frame, textvariable=self.xunfei_api_key_var, width=25).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(xunfei_frame, text="API Secret:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(xunfei_frame, textvariable=self.xunfei_api_secret_var, width=25).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["xunfei-tts"] = xunfei_frame
        
        # 创建腾讯TTS API设置组件
        tencent_frame = ttk.Frame(self.api_frame)
        ttk.Label(tencent_frame, text="Secret ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(tencent_frame, textvariable=self.tencent_secret_id_var, width=25).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(tencent_frame, text="Secret Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(tencent_frame, textvariable=self.tencent_secret_key_var, width=25).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["tencent-tts"] = tencent_frame
        
        # 创建Edge TTS API设置组件 (无需API密钥)
        edge_frame = ttk.Frame(self.api_frame)
        ttk.Label(edge_frame, text="Edge TTS无需API密钥，可直接使用", foreground="#1976D2").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_widgets["edge-tts"] = edge_frame
        
        # 隐藏API设置分组
        self.api_frame.pack_forget()
        
        # API 设置已废弃，避免后续逻辑重复显示
        self.update_api_settings()
        
        # 创建设置组 - 语音参数
        params_frame = self.create_section_frame("语音参数")
        
        # 音量滑块 (UI范围: 0-100, 实际值范围: 0.1-2.0)
        volume_frame = ttk.Frame(params_frame)
        volume_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(volume_frame, text="音量:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        volume_scale = ttk.Scale(volume_frame, from_=0, to=100, orient=tk.HORIZONTAL, 
                               variable=self.volume_var, length=120,  # 增加长度到120
                               command=self.on_volume_change)
        volume_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        volume_value_frame = ttk.Frame(volume_frame)
        volume_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.volume_entry = ttk.Entry(volume_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.volume_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.volume_entry.insert(0, str(round(self.volume_var.get())))
        self.volume_entry.bind('<Return>', self.on_volume_entry_change)
        self.volume_entry.bind('<FocusOut>', self.on_volume_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(volume_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)
        
        # 语速滑块 (UI范围: -100-100, 实际值范围: 0.5-2.0)
        speed_frame = ttk.Frame(params_frame)
        speed_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(speed_frame, text="语速:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        speed_scale = ttk.Scale(speed_frame, from_=-100, to=100, orient=tk.HORIZONTAL, 
                              variable=self.speed_var, length=120, # 增加长度到120
                              command=self.on_speed_change)
        speed_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        speed_value_frame = ttk.Frame(speed_frame)
        speed_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.speed_entry = ttk.Entry(speed_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.speed_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.speed_entry.insert(0, str(round(self.speed_var.get())))
        self.speed_entry.bind('<Return>', self.on_speed_entry_change)
        self.speed_entry.bind('<FocusOut>', self.on_speed_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(speed_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)

        # 音调滑块 (UI范围: -100-100, 实际值范围: 0.5-2.0)
        pitch_frame = ttk.Frame(params_frame)
        pitch_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(pitch_frame, text="音调:", width=5).pack(side=tk.LEFT, padx=5)
        
        # 大幅缩短滑块，增大数值框
        pitch_scale = ttk.Scale(pitch_frame, from_=-100, to=100, orient=tk.HORIZONTAL, 
                              variable=self.pitch_var, length=120, # 增加长度到120
                              command=self.on_pitch_change)
        pitch_scale.pack(side=tk.LEFT, padx=5)
        
        # 框架容纳数值和单位
        pitch_value_frame = ttk.Frame(pitch_frame)
        pitch_value_frame.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True) # 让框架填充剩余空间

        # 使用ttk.Entry并设置样式 (增大宽度)
        self.pitch_entry = ttk.Entry(pitch_value_frame, width=5, justify='center', style="ValueDisplay.TEntry")
        self.pitch_entry.pack(side=tk.LEFT, padx=(0, 2))
        self.pitch_entry.insert(0, str(round(self.pitch_var.get())))
        self.pitch_entry.bind('<Return>', self.on_pitch_entry_change)
        self.pitch_entry.bind('<FocusOut>', self.on_pitch_entry_change)
        
        # 添加单位标签 (恢复加粗)
        ttk.Label(pitch_value_frame, text="%", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)

        # 音量放大（dB）输入框 - 移动到音调下方
        amplify_frame = ttk.Frame(params_frame)
        amplify_frame.pack(fill=tk.X, pady=5)
        ttk.Label(amplify_frame, text="音量放大:", width=8).pack(side=tk.LEFT, padx=5)
        self.amplify_entry = ttk.Entry(amplify_frame, textvariable=self.amplify_db_var, width=6, justify='center', style="ValueDisplay.TEntry")
        self.amplify_entry.pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(amplify_frame, text="dB", font=("微软雅黑", 10, "bold")).pack(side=tk.LEFT)
        # 绑定回车和失焦事件，校验输入
        self.amplify_entry.bind('<Return>', self.on_amplify_entry_change)
        self.amplify_entry.bind('<FocusOut>', self.on_amplify_entry_change)
        
        # 音量标准化复选框 - 移动到音调下方
        normalize_check = ttk.Checkbutton(params_frame, text="启用音量标准化", variable=self.normalize_audio_var)
        normalize_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(self.settings_container)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 保存设置按钮
        save_btn = ttk.Button(button_frame, text="保存设置", command=self.save_settings, width=10)
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 加载设置按钮改为重置设置按钮
        reset_btn = ttk.Button(button_frame, text="重置设置", command=self.reset_settings, width=10)
        reset_btn.pack(side=tk.RIGHT, padx=5)
        
        # 创建完所有组件后，更新UI显示以反映已加载的设置
        self.update_ui_after_load()
    
    def create_header(self, text, color="#333333"):
        """创建带有颜色的标题"""
        header_frame = ttk.Frame(self.settings_container)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        header_label = ttk.Label(header_frame, 
                               text=text, 
                               font=("微软雅黑", 18, "bold"),
                               foreground=color)
        header_label.pack(anchor=tk.W)
        
        # 添加分隔线
        separator = ttk.Separator(self.settings_container, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 10))
        
    def create_section_frame(self, title):
        """创建带标题的设置分组"""
        # 创建分组容器
        section_frame = ttk.LabelFrame(self.settings_container, text=title)
        section_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        return section_frame
    
    def on_engine_change(self, event=None):
        """引擎变化时的回调"""
        # 更新API设置
        self.update_api_settings()
        
        # 更新语音列表
        self.update_voice_list()
    
    def update_api_settings(self):
        """根据当前选择的引擎显示对应的API设置"""
        # API 设置面板已在新版中隐藏，此处直接返回
        return
    
    def update_voice_list(self):
        """更新语音下拉框的选项"""
        # 获取当前选择的引擎
        engine = self.engine_var.get()
        
        # 若读取到的引擎不在支持列表中（旧版本遗留设置），强制切换回 edge-tts
        if engine not in self.engine_list:
            engine = "edge-tts"
            self.engine_var.set(engine)
        
        # 根据引擎获取对应的语音列表
        if engine == "edge-tts":
            # Edge TTS使用分组语音列表
            voices = []
            for category, voice_list in self.edge_voice_list.items():
                for voice in voice_list:
                    voices.append(voice)
        elif engine == "azure-tts":
            voices = self.azure_voice_list
        elif engine == "baidu-tts":
            voices = self.baidu_voice_list
        elif engine == "xunfei-tts":
            voices = self.xunfei_voice_list
        elif engine == "tencent-tts":
            voices = self.tencent_voice_list
        else:
            voices = []
        
        # 更新语音下拉框的值
        self.voice_combo["values"] = voices
        
        # 如果当前的语音不在列表中，选择第一个
        if self.voice_var.get() not in voices and voices:
            self.voice_var.set(voices[0])
    
    def on_speed_change(self, value):
        """语速变化时的回调"""
        # 获取UI值 (-100到100)
        speed_ui = round(float(value))
        
        # 更新UI标签
        self.speed_entry.delete(0, tk.END)
        self.speed_entry.insert(0, str(speed_ui))
        
        # 将UI值转换为实际值 (0.5-2.0)
        # -100 对应 0.5, 0 对应 1.0, 100 对应 2.0
        actual_speed = 1.0 + (speed_ui / 100.0)
        # 限制范围
        actual_speed = max(0.5, min(2.0, actual_speed))
        
        # 更新设置
        self.settings["speed"] = actual_speed
    
    def on_pitch_change(self, value):
        """音调变化时的回调"""
        # 获取UI值 (-100到100)
        pitch_ui = round(float(value))
        
        # 更新UI标签
        self.pitch_entry.delete(0, tk.END)
        self.pitch_entry.insert(0, str(pitch_ui))
        
        # 将UI值转换为实际值 (0.5-2.0)
        # -100 对应 0.5, 0 对应 1.0, 100 对应 2.0
        actual_pitch = 1.0 + (pitch_ui / 100.0)
        # 限制范围
        actual_pitch = max(0.5, min(2.0, actual_pitch))
        
        # 更新设置
        self.settings["pitch"] = actual_pitch
    
    def on_volume_change(self, value):
        """音量变化时的回调"""
        # 获取UI值 (0到100)
        volume_ui = round(float(value))
        
        # 更新UI标签
        self.volume_entry.delete(0, tk.END)
        self.volume_entry.insert(0, str(volume_ui))
        
        # 将UI值转换为实际值 (0.1-2.0)
        # 0 对应 0.1, 100 对应 2.0
        actual_volume = 0.1 + (volume_ui / 100.0) * 1.9
        
        # 更新设置
        self.settings["volume"] = actual_volume
    
    def on_setting_change(self, event=None):
        """当设置改变时回调这个函数"""
        # 重新计算实际的设置值 - 这是从UI值转换回实际的配置值
        self._update_actual_values()
        
        # 记录变更
        self.settings["voice"] = self.voice_var.get()
        self.settings["engine"] = self.engine_var.get()
        
        return self.settings
    
    def _update_actual_values(self):
        """更新实际值"""
        # 只更新滑块设置，其他设置已在其他地方更新
        self.update_api_config_values()
    
    def update_api_config_values(self):
        """更新API配置值到设置字典中"""
        self.settings["azure_key"] = self.azure_key_var.get()
        self.settings["azure_region"] = self.azure_region_var.get()
        self.settings["baidu_app_id"] = self.baidu_app_id_var.get()
        self.settings["baidu_api_key"] = self.baidu_api_key_var.get()
        self.settings["baidu_secret_key"] = self.baidu_secret_key_var.get()
        self.settings["xunfei_app_id"] = self.xunfei_app_id_var.get()
        self.settings["xunfei_api_key"] = self.xunfei_api_key_var.get()
        self.settings["xunfei_api_secret"] = self.xunfei_api_secret_var.get()
        self.settings["tencent_secret_id"] = self.tencent_secret_id_var.get()
        self.settings["tencent_secret_key"] = self.tencent_secret_key_var.get()
    
    def save_settings(self):
        """保存设置到文件"""
        try:
            # 更新所有设置值
            self.settings.update({
                "voice": self.voice_var.get(),
                "speed": self.settings["speed"],
                "pitch": self.settings["pitch"],
                "volume": self.settings["volume"],
                "engine": self.engine_var.get(),
                "amplify_db": float(self.amplify_db_var.get()),  # 新增音量放大dB
                "normalize_audio": self.normalize_audio_var.get(), # 新增音量标准化
                # 批量分割
                "first_split_size": int(self.first_split_size_var.get()),
                "split_size": int(self.split_size_var.get()),
            })
            
            # 更新API设置
            self.update_api_config_values()
            
            # 获取设置文件路径
            settings_dir = os.path.expanduser("~/.tts_tool")
            if not os.path.exists(settings_dir):
                os.makedirs(settings_dir)
            
            settings_file = os.path.join(settings_dir, "settings.json")
            
            # 保存为JSON
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(self.settings, f, indent=4)
            
            # 显示成功消息
            messagebox.showinfo("保存成功", "设置已保存")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("保存失败", f"保存设置失败: {str(e)}")
    
    def load_settings(self):
        """从文件加载设置"""
        try:
            # 获取设置文件路径
            settings_file = os.path.join(os.path.expanduser("~/.tts_tool"), "settings.json")
            
            # 如果文件存在，加载设置
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)
                
                # 更新配置
                for key, value in loaded_settings.items():
                    if key in self.settings:
                        self.settings[key] = value
                
                # 新增同步amplify_db
                if "amplify_db" in loaded_settings:
                    self.settings["amplify_db"] = loaded_settings["amplify_db"]
                
                # 新增同步normalize_audio
                if "normalize_audio" in loaded_settings:
                    self.settings["normalize_audio"] = loaded_settings["normalize_audio"]
                
                # 新增同步first_split_size
                if "first_split_size" in loaded_settings:
                    self.settings["first_split_size"] = loaded_settings["first_split_size"]
                
                # 新增同步split_size
                if "split_size" in loaded_settings:
                    self.settings["split_size"] = loaded_settings["split_size"]
                
                # 确保所有需要的变量都已经初始化
                if not hasattr(self, 'voice_var') or not hasattr(self, 'engine_var'):
                    print("变量尚未初始化，跳过UI更新")
                    return
                
                # 更新变量
                try:
                    self.voice_var.set(self.settings["voice"])
                    self.engine_var.set(self.settings["engine"])
                    
                    # 计算UI显示值
                    # 音量: 0.1-2.0 -> 0-100
                    volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
                    self.volume_var.set(volume_ui)
                    
                    # 语速: 0.5-2.0 -> -100-100
                    speed_ui = (self.settings["speed"] - 1.0) * 100
                    self.speed_var.set(speed_ui)
                    
                    # 音调: 0.5-2.0 -> -100-100
                    pitch_ui = (self.settings["pitch"] - 1.0) * 100
                    self.pitch_var.set(pitch_ui)
                    
                    # 新增同步amplify_db到输入框
                    self.amplify_db_var.set(str(self.settings.get("amplify_db", "0")))
                    
                    # 新增同步normalize_audio到复选框
                    self.normalize_audio_var.set(self.settings.get("normalize_audio", False))
                    
                    # 新增同步first_split_size到输入框
                    self.first_split_size_var.set(str(self.settings.get("first_split_size", 50000)))
                    
                    # 新增同步split_size到输入框
                    self.split_size_var.set(str(self.settings.get("split_size", 50000)))
                    
                    # 更新API设置
                    self.azure_key_var.set(self.settings.get("azure_key", ""))
                    self.azure_region_var.set(self.settings.get("azure_region", ""))
                    self.baidu_app_id_var.set(self.settings.get("baidu_app_id", ""))
                    self.baidu_api_key_var.set(self.settings.get("baidu_api_key", ""))
                    self.baidu_secret_key_var.set(self.settings.get("baidu_secret_key", ""))
                    self.xunfei_app_id_var.set(self.settings.get("xunfei_app_id", ""))
                    self.xunfei_api_key_var.set(self.settings.get("xunfei_api_key", ""))
                    self.xunfei_api_secret_var.set(self.settings.get("xunfei_api_secret", ""))
                    self.tencent_secret_id_var.set(self.settings.get("tencent_secret_id", ""))
                    self.tencent_secret_key_var.set(self.settings.get("tencent_secret_key", ""))
                    
                    print("成功加载设置")
                except Exception as e:
                    # 捕获任何可能的UI更新错误
                    print(f"更新UI变量失败: {str(e)}")
                    # 继续流程，不抛出异常
        except Exception as e:
            print(f"加载设置失败: {str(e)}")
            # 继续流程，不抛出异常
    
    def load_advanced_settings(self):
        """加载高级设置"""
        try:
            # 获取高级设置文件路径 - 使用新的文件名
            settings_dir = os.path.expanduser("~/.tts_tool")
            settings_file = os.path.join(settings_dir, "tts_advanced_config.json")

            # 检查是否存在旧的配置文件
            old_settings_file = os.path.join(settings_dir, "advanced_settings.json")
            
            # 设置默认值
            advanced_settings = {
                "remove_empty_lines": True,
                "concurrent_tasks": 8,  # 提高默认值为8，更好利用CPU
                "retry_count": 5,
                "retry_interval": 10,
                "batch_size": 3000,  # 修改默认批次大小为3000
            }
            
            # 如果新文件存在，加载设置
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)

                # 更新配置
                for key, value in loaded_settings.items():
                    if key in advanced_settings:
                        advanced_settings[key] = value
            elif os.path.exists(old_settings_file):
                # 如果新文件不存在但旧文件存在，加载旧文件
                with open(old_settings_file, "r", encoding="utf-8") as f:
                    loaded_settings = json.load(f)

                # 更新配置
                for key, value in loaded_settings.items():
                    if key in advanced_settings:
                        advanced_settings[key] = value
                print("检测到旧的高级设置文件，建议重新保存设置以迁移到新文件名")
                        
            return advanced_settings
        except Exception as e:
            print(f"加载高级设置失败: {str(e)}")
            # 返回默认值
            return {
                "remove_empty_lines": True,
                "concurrent_tasks": 8,  # 提高默认值为8，更好利用CPU
                "retry_count": 5,
                "retry_interval": 10,
                "batch_size": 10000,
            }
    
    def get_settings(self):
        """获取当前设置"""
        # 更新设置
        self.settings.update({
            "voice": self.voice_var.get(),
            "speed": self.settings["speed"],
            "pitch": self.settings["pitch"],
            "volume": self.settings["volume"],
            "engine": self.engine_var.get(),
            "amplify_db": float(self.amplify_db_var.get()),  # 新增音量放大dB
            "normalize_audio": self.normalize_audio_var.get(), # 新增音量标准化
            # API设置
            "azure_key": self.azure_key_var.get(),
            "azure_region": self.azure_region_var.get(),
            "baidu_app_id": self.baidu_app_id_var.get(),
            "baidu_api_key": self.baidu_api_key_var.get(),
            "baidu_secret_key": self.baidu_secret_key_var.get(),
            "xunfei_app_id": self.xunfei_app_id_var.get(),
            "xunfei_api_key": self.xunfei_api_key_var.get(),
            "xunfei_api_secret": self.xunfei_api_secret_var.get(),
            "tencent_secret_id": self.tencent_secret_id_var.get(),
            "tencent_secret_key": self.tencent_secret_key_var.get(),
            # 批量分割
            "first_split_size": int(self.first_split_size_var.get()),
            "split_size": int(self.split_size_var.get()),
        })
        
        # 合并高级设置
        all_settings = {**self.settings, **self.advanced_settings}
        
        return all_settings

    # 新增Entry回调函数
    def on_volume_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.volume_entry.get())
            # 限制在0-100范围内
            value = max(0, min(100, value))
            # 更新滑块和变量
            self.volume_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_volume_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.volume_entry.delete(0, tk.END)
            self.volume_entry.insert(0, str(round(self.volume_var.get())))
    
    def on_speed_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.speed_entry.get())
            # 限制在-100到100范围内
            value = max(-100, min(100, value))
            # 更新滑块和变量
            self.speed_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_speed_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.speed_entry.delete(0, tk.END)
            self.speed_entry.insert(0, str(round(self.speed_var.get())))
    
    def on_pitch_entry_change(self, event=None):
        try:
            # 获取输入值并验证
            value = int(self.pitch_entry.get())
            # 限制在-100到100范围内
            value = max(-100, min(100, value))
            # 更新滑块和变量
            self.pitch_var.set(value)
            # 手动调用滑块回调以更新实际值
            self.on_pitch_change(value)
        except ValueError:
            # 如果输入不是有效数字，恢复原值
            self.pitch_entry.delete(0, tk.END)
            self.pitch_entry.insert(0, str(round(self.pitch_var.get())))
    
    def on_amplify_entry_change(self, event=None):
        """音量放大dB输入框回调，校验并同步到变量"""
        try:
            value = float(self.amplify_entry.get())
            # 限制范围（-30到+30dB，防止极端输入）
            value = max(-30, min(30, value))
            self.amplify_db_var.set(str(value))
            self.settings["amplify_db"] = value
        except ValueError:
            # 恢复原值
            self.amplify_entry.delete(0, tk.END)
            self.amplify_entry.insert(0, str(self.settings.get("amplify_db", "0")))
    
    def reset_settings(self):
        """重置所有设置为默认值"""
        try:
            # 确认是否重置
            if messagebox.askyesno("确认重置", "确定要重置所有设置为默认值吗？"):
                # 重置为默认值
                self.settings = {
                    "voice": "zh-CN-XiaoxiaoNeural",
                    "engine": "edge-tts",
                    "speed": 1.0,
                    "pitch": 1.0,
                    "volume": 1.0,
                    "azure_key": "",
                    "azure_region": "eastasia",
                    "baidu_app_id": "",
                    "baidu_api_key": "",
                    "baidu_secret_key": "",
                    "xunfei_app_id": "",
                    "xunfei_api_key": "",
                    "xunfei_api_secret": "",
                    "tencent_secret_id": "",
                    "tencent_secret_key": "",
                    "amplify_db": 0,
                    "normalize_audio": False,
                    # 批量分割预设
                    "first_split_size": 50000,
                    "split_size": 50000,
                }
                
                # 更新UI变量
                # 更新基本设置变量
                self.voice_var.set(self.settings["voice"])
                self.engine_var.set(self.settings["engine"])
                
                # 计算并更新UI滑块位置
                # 音量: 0.1-2.0 -> 0-100
                volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
                # 语速: 0.5-2.0 -> -100-100
                speed_ui = (self.settings["speed"] - 1.0) * 100
                # 音调: 0.5-2.0 -> -100-100
                pitch_ui = (self.settings["pitch"] - 1.0) * 100
                
                # 设置UI变量
                self.volume_var.set(volume_ui)
                self.speed_var.set(speed_ui)
                self.pitch_var.set(pitch_ui)
                
                # 新增同步amplify_db到输入框
                self.amplify_db_var.set(str(self.settings.get("amplify_db", "0")))
                
                # 新增同步normalize_audio到复选框
                self.normalize_audio_var.set(self.settings.get("normalize_audio", False))
                
                # 新增同步first_split_size到输入框
                self.first_split_size_var.set(str(self.settings.get("first_split_size", 50000)))
                
                # 新增同步split_size到输入框
                self.split_size_var.set(str(self.settings.get("split_size", 50000)))
                
                # 更新标签显示
                self.speed_entry.delete(0, tk.END)
                self.speed_entry.insert(0, str(round(speed_ui)))
                
                self.pitch_entry.delete(0, tk.END)
                self.pitch_entry.insert(0, str(round(pitch_ui)))
                
                self.volume_entry.delete(0, tk.END)
                self.volume_entry.insert(0, str(round(volume_ui)))
                
                # 重置API设置变量
                self.azure_key_var.set("")
                self.azure_region_var.set("")
                self.baidu_app_id_var.set("")
                self.baidu_api_key_var.set("")
                self.baidu_secret_key_var.set("")
                self.xunfei_app_id_var.set("")
                self.xunfei_api_key_var.set("")
                self.xunfei_api_secret_var.set("")
                self.tencent_secret_id_var.set("")
                self.tencent_secret_key_var.set("")
                
                # 更新API设置显示
                self.update_api_settings()
                
                # 更新语音列表
                self.update_voice_list()
                
                # 显示成功消息
                messagebox.showinfo("重置成功", "所有设置已重置为默认值")
        except Exception as e:
            # 显示错误消息
            messagebox.showerror("重置失败", f"重置设置失败: {str(e)}")
    
    def update_ui_after_load(self):
        """在创建完UI组件后更新显示"""
        try:
            # 根据引擎更新UI
            self.update_voice_list()
            self.update_api_settings()
            
            # 更新数值显示
            volume_ui = (self.settings["volume"] - 0.1) * (100 / 1.9)
            self.volume_entry.delete(0, tk.END)
            self.volume_entry.insert(0, str(round(volume_ui)))
            
            speed_ui = (self.settings["speed"] - 1.0) * 100
            self.speed_entry.delete(0, tk.END)
            self.speed_entry.insert(0, str(round(speed_ui)))
            
            pitch_ui = (self.settings["pitch"] - 1.0) * 100
            self.pitch_entry.delete(0, tk.END)
            self.pitch_entry.insert(0, str(round(pitch_ui)))
        except Exception as e:
            print(f"更新UI显示失败: {str(e)}")
            # 继续流程，不抛出异常 